import os
import google.generativeai as genai
from src.models.message import Message
from src.models.conversation import Conversation
import logging

class AIService:
    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        if self.api_key:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-pro')
        else:
            logging.warning("Gemini API key not found. AI features will be limited.")
            self.model = None
    
    def generate_response(self, user_message, conversation_id=None):
        """Generate AI response using Gemini API"""
        try:
            if not self.model:
                return "AI service is not properly configured. Please check your API keys."
            
            # Get conversation context if available
            context = self._get_conversation_context(conversation_id)
            
            # Prepare the prompt with context
            prompt = self._prepare_prompt(user_message, context)
            
            # Generate response
            response = self.model.generate_content(prompt)
            
            if response.text:
                return response.text
            else:
                return "I'm sorry, I couldn't generate a response at the moment."
                
        except Exception as e:
            logging.error(f"Error generating AI response: {str(e)}")
            return "I apologize, but I'm experiencing some technical difficulties. Please try again."
    
    def generate_content(self, content_type, prompt, parameters=None):
        """Generate specific content types (scripts, stories, etc.)"""
        try:
            if not self.model:
                return "AI service is not properly configured."
            
            # Prepare specialized prompts based on content type
            if content_type == 'script':
                full_prompt = self._prepare_script_prompt(prompt, parameters)
            elif content_type == 'story':
                full_prompt = self._prepare_story_prompt(prompt, parameters)
            elif content_type == 'description':
                full_prompt = self._prepare_description_prompt(prompt, parameters)
            else:
                full_prompt = prompt
            
            response = self.model.generate_content(full_prompt)
            
            if response.text:
                return response.text
            else:
                return "Unable to generate content at this time."
                
        except Exception as e:
            logging.error(f"Error generating content: {str(e)}")
            return f"Error generating {content_type}: {str(e)}"
    
    def analyze_command_intent(self, command):
        """Analyze natural language command to extract intent and parameters"""
        try:
            if not self.model:
                return {"intent": "unknown", "parameters": {}, "confidence": 0.0}
            
            prompt = f"""
            Analyze the following command and extract the intent and parameters:
            Command: "{command}"
            
            Please respond in JSON format with:
            - intent: the main action the user wants to perform
            - parameters: any specific parameters or values mentioned
            - confidence: confidence level (0.0 to 1.0)
            - explanation: brief explanation of the interpretation
            
            Focus on YouTube content creation related intents like:
            - upload_video, create_script, generate_thumbnail, analyze_performance, etc.
            """
            
            response = self.model.generate_content(prompt)
            
            if response.text:
                # Try to parse JSON response
                import json
                try:
                    result = json.loads(response.text)
                    return result
                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    return {
                        "intent": "general_query",
                        "parameters": {"query": command},
                        "confidence": 0.5,
                        "explanation": response.text
                    }
            else:
                return {"intent": "unknown", "parameters": {}, "confidence": 0.0}
                
        except Exception as e:
            logging.error(f"Error analyzing command intent: {str(e)}")
            return {"intent": "error", "parameters": {"error": str(e)}, "confidence": 0.0}
    
    def _get_conversation_context(self, conversation_id, limit=10):
        """Get recent conversation context"""
        if not conversation_id:
            return []
        
        try:
            messages = Message.query.filter_by(conversation_id=conversation_id)\
                .order_by(Message.created_at.desc())\
                .limit(limit)\
                .all()
            
            context = []
            for message in reversed(messages):  # Reverse to get chronological order
                context.append({
                    'role': 'user' if message.message_type == 'user' else 'assistant',
                    'content': message.content
                })
            
            return context
        except Exception as e:
            logging.error(f"Error getting conversation context: {str(e)}")
            return []
    
    def _prepare_prompt(self, user_message, context):
        """Prepare prompt with conversation context"""
        system_prompt = """You are a helpful AI assistant for a YouTube content creator. 
        You can help with script writing, story development, content ideas, and general conversation.
        Be creative, engaging, and supportive. Keep responses conversational and helpful."""
        
        if context:
            context_text = "\n".join([f"{msg['role']}: {msg['content']}" for msg in context[-5:]])  # Last 5 messages
            prompt = f"{system_prompt}\n\nConversation context:\n{context_text}\n\nUser: {user_message}\nAssistant:"
        else:
            prompt = f"{system_prompt}\n\nUser: {user_message}\nAssistant:"
        
        return prompt
    
    def _prepare_script_prompt(self, prompt, parameters):
        """Prepare prompt for script generation"""
        duration = parameters.get('duration', '5-10 minutes') if parameters else '5-10 minutes'
        style = parameters.get('style', 'engaging and informative') if parameters else 'engaging and informative'
        
        return f"""
        Create a YouTube video script with the following requirements:
        - Topic: {prompt}
        - Duration: {duration}
        - Style: {style}
        
        Please include:
        1. Hook/Introduction (first 15 seconds)
        2. Main content sections
        3. Call-to-action
        4. Conclusion
        
        Format the script with clear sections and timing notes.
        """
    
    def _prepare_story_prompt(self, prompt, parameters):
        """Prepare prompt for story generation"""
        genre = parameters.get('genre', 'general') if parameters else 'general'
        length = parameters.get('length', 'medium') if parameters else 'medium'
        
        return f"""
        Create an engaging story based on: {prompt}
        
        Requirements:
        - Genre: {genre}
        - Length: {length}
        - Make it suitable for video content
        - Include visual descriptions for potential scenes
        
        Focus on creating compelling narrative that would work well as video content.
        """
    
    def _prepare_description_prompt(self, prompt, parameters):
        """Prepare prompt for description generation"""
        return f"""
        Create a compelling YouTube video description for: {prompt}
        
        Include:
        - Engaging opening
        - Key points covered
        - Relevant hashtags
        - Call-to-action for likes, comments, and subscriptions
        
        Make it SEO-friendly and engaging for viewers.
        """

