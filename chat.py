from flask import Blueprint, request, jsonify
from src.models.user import db
from src.models.conversation import Conversation
from src.models.message import Message
from src.services.ai_service import AIService
import logging

chat_bp = Blueprint('chat', __name__)
ai_service = AIService()

@chat_bp.route('/conversations', methods=['GET'])
def get_conversations():
    """Get all conversations for a user"""
    try:
        # For now, we'll use a default user_id of 1
        # In a real app, this would come from authentication
        user_id = request.args.get('user_id', 1, type=int)
        
        conversations = Conversation.query.filter_by(user_id=user_id, is_active=True).order_by(Conversation.updated_at.desc()).all()
        
        return jsonify({
            'success': True,
            'conversations': [conv.to_dict() for conv in conversations]
        })
    except Exception as e:
        logging.error(f"Error getting conversations: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@chat_bp.route('/conversations', methods=['POST'])
def create_conversation():
    """Create a new conversation"""
    try:
        data = request.get_json()
        user_id = data.get('user_id', 1)  # Default user_id
        title = data.get('title', 'New Conversation')
        
        conversation = Conversation(
            title=title,
            user_id=user_id
        )
        
        db.session.add(conversation)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'conversation': conversation.to_dict()
        }), 201
    except Exception as e:
        logging.error(f"Error creating conversation: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@chat_bp.route('/conversations/<int:conversation_id>', methods=['GET'])
def get_conversation(conversation_id):
    """Get a specific conversation with messages"""
    try:
        conversation = Conversation.query.get_or_404(conversation_id)
        
        return jsonify({
            'success': True,
            'conversation': conversation.to_dict_with_messages()
        })
    except Exception as e:
        logging.error(f"Error getting conversation: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@chat_bp.route('/conversations/<int:conversation_id>', methods=['DELETE'])
def delete_conversation(conversation_id):
    """Delete a conversation"""
    try:
        conversation = Conversation.query.get_or_404(conversation_id)
        conversation.is_active = False
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Conversation deleted successfully'
        })
    except Exception as e:
        logging.error(f"Error deleting conversation: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@chat_bp.route('/conversations/<int:conversation_id>/messages', methods=['GET'])
def get_messages(conversation_id):
    """Get messages for a conversation"""
    try:
        conversation = Conversation.query.get_or_404(conversation_id)
        messages = Message.query.filter_by(conversation_id=conversation_id).order_by(Message.created_at.asc()).all()
        
        return jsonify({
            'success': True,
            'messages': [message.to_dict() for message in messages]
        })
    except Exception as e:
        logging.error(f"Error getting messages: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@chat_bp.route('/conversations/<int:conversation_id>/messages', methods=['POST'])
def send_message(conversation_id):
    """Send a message and get AI response"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        message_type = data.get('message_type', 'user')
        
        if not content:
            return jsonify({'success': False, 'error': 'Message content is required'}), 400
        
        conversation = Conversation.query.get_or_404(conversation_id)
        
        # Save user message
        user_message = Message(
            conversation_id=conversation_id,
            content=content,
            message_type=message_type
        )
        db.session.add(user_message)
        
        # Generate AI response
        try:
            ai_response = ai_service.generate_response(content, conversation_id)
            
            # Save AI response
            ai_message = Message(
                conversation_id=conversation_id,
                content=ai_response,
                message_type='assistant'
            )
            db.session.add(ai_message)
            
            # Update conversation timestamp
            conversation.updated_at = db.func.current_timestamp()
            
            db.session.commit()
            
            return jsonify({
                'success': True,
                'user_message': user_message.to_dict(),
                'ai_response': ai_message.to_dict()
            })
            
        except Exception as ai_error:
            logging.error(f"AI service error: {str(ai_error)}")
            # Still save the user message even if AI fails
            db.session.commit()
            
            return jsonify({
                'success': False,
                'error': 'Failed to generate AI response',
                'user_message': user_message.to_dict()
            }), 500
            
    except Exception as e:
        logging.error(f"Error sending message: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@chat_bp.route('/generate-content', methods=['POST'])
def generate_content():
    """Generate content using AI (scripts, stories, etc.)"""
    try:
        data = request.get_json()
        content_type = data.get('type', 'script')  # script, story, etc.
        prompt = data.get('prompt', '')
        parameters = data.get('parameters', {})
        
        if not prompt:
            return jsonify({'success': False, 'error': 'Prompt is required'}), 400
        
        generated_content = ai_service.generate_content(content_type, prompt, parameters)
        
        return jsonify({
            'success': True,
            'content': generated_content,
            'type': content_type
        })
        
    except Exception as e:
        logging.error(f"Error generating content: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

