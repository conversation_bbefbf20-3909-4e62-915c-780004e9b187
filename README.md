# 🧠 AI Personality Training Voice Chat

A real-time voice-to-voice AI chat application designed for training AI personalities. Create, save, and switch between different AI characters with unique personalities, speech patterns, and behaviors!

## ✨ Features

### 🗣️ Voice-First Experience
- **Automatic Voice Detection**: No need to press record/stop buttons
- **Real-time Speech Recognition**: Converts your speech to text automatically
- **Natural Voice Responses**: AI responds with synthesized speech in character
- **Seamless Conversation Flow**: Feels like talking to a real person

### 🧠 Advanced Personality Training System
- **Deep Learning Memory**: AI remembers every interaction and learns from them
- **Training Examples**: Feed specific scenarios and expected responses
- **Behavioral Pattern Recognition**: AI automatically learns patterns from examples
- **Semantic Memory Search**: Uses OpenAI embeddings for intelligent memory retrieval
- **Feedback Learning**: Correct the AI and it learns from mistakes
- **Personality Evolution**: AI can develop and grow its personality over time
- **Training Mode**: Enhanced receptiveness to personality adjustments
- **Voice Commands**: Train personalities using natural speech
- **Persistent Memory**: Remembers everything forever across sessions

### 🎵 Voice Technology
- **ElevenLabs TTS**: High-quality voice synthesis
- **Google Speech Recognition**: Accurate speech-to-text
- **Voice Activity Detection**: Automatically detects when you're speaking
- **Audio Level Monitoring**: Visual feedback for voice input

## 🚀 Quick Start

1. **Run the setup script:**
   ```bash
   ./setup.sh
   ```

2. **Configure API keys:**
   ```bash
   cp .env.example .env
   # Edit .env and add your API keys
   ```

3. **Start the backend:**
   ```bash
   python3 main.py
   ```

4. **Start the frontend (in a new terminal):**
   ```bash
   npm run dev
   ```

5. **Open your browser:**
   Navigate to `http://localhost:3000`

## 🔑 Required API Keys

### Google Gemini API
- Get your key from: https://makersuite.google.com/app/apikey
- Add to `.env` as `GEMINI_API_KEY`

### ElevenLabs API
- Get your key from: https://elevenlabs.io/
- Add to `.env` as `ELEVENLABS_API_KEY`

### OpenAI API (Optional but Recommended)
- Get your key from: https://platform.openai.com/api-keys
- Add to `.env` as `OPENAI_API_KEY`
- Enables advanced memory search and pattern recognition

## 🎯 How to Use

### Personality Training
1. **Click the brain icon (🧠)** to open the Personality Manager
2. **Set a personality** by describing the character you want to create
3. **Save personalities** with names for easy switching
4. **Use voice commands** like "Set personality to be a wise mentor"
5. **Enable training mode** for more responsive personality adjustments

### Voice Chat
1. Click the **wave button** to start listening
2. **Speak naturally** - the app will detect your voice automatically
3. **The AI responds in character** - maintaining the personality you've set
4. **Continue the conversation** - no buttons needed!

### Visual Indicators
- **Blue wave**: Ready to listen
- **Green pulse**: Detecting your voice
- **Audio level bar**: Shows your voice volume
- **Avatar states**: Idle → Listening → Thinking → Speaking

### Personality Commands
- **"Set personality to [description]"** - Define a new personality
- **"Save personality [name]"** - Save current personality
- **"Load personality [name]"** - Switch to saved personality
- **"Show personality"** - Display current personality
- **"Training mode on/off"** - Toggle training mode
- **"Reset personality"** - Return to neutral mode

## 🏗️ Architecture

### Backend (Python/Flask)
- **Flask + SocketIO**: Real-time communication
- **SQLAlchemy**: Database management
- **Google Gemini**: AI responses
- **ElevenLabs**: Voice synthesis
- **SpeechRecognition**: Speech-to-text

### Frontend (React/TypeScript)
- **React + TypeScript**: Modern UI framework
- **Socket.IO**: Real-time communication
- **Web Audio API**: Voice recording and playback
- **Voice Activity Detection**: Automatic speech detection

## 🔧 Technical Details

### Voice Processing Pipeline
1. **Voice Activity Detection**: Monitors audio levels
2. **Automatic Recording**: Starts when speech detected
3. **Silence Detection**: Stops after 1.5s of silence
4. **Speech Recognition**: Converts audio to text
5. **AI Processing**: Generates response
6. **Voice Synthesis**: Converts response to speech
7. **Automatic Playback**: Plays AI voice response

### Real-time Features
- WebSocket communication for instant responses
- Streaming audio for low latency
- Visual feedback for all voice states
- Error handling and recovery

## 🎨 UI/UX Design

- **Glass morphism**: Modern, translucent design
- **Voice-first layout**: Minimal text input prominence
- **Real-time feedback**: Visual indicators for all states
- **Responsive design**: Works on desktop and mobile
- **Accessibility**: Clear visual and audio feedback

## 🛠️ Development

### Project Structure
```
├── src/
│   ├── components/        # React components
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   ├── services/         # Business logic
│   └── websocket/        # Real-time handlers
├── static/               # Built frontend files
├── database/             # SQLite database
└── requirements.txt      # Python dependencies
```

### Key Components
- **VoiceControls**: Voice activity detection and UI
- **ChatHandler**: WebSocket message processing
- **VoiceService**: Speech recognition and synthesis
- **AIService**: Gemini API integration

## 🐛 Troubleshooting

### Voice Not Working
- Check microphone permissions
- Ensure API keys are configured
- Try refreshing the page
- Check browser console for errors

### Audio Issues
- Verify speaker/headphone connection
- Check volume settings
- Try different browser
- Ensure ElevenLabs API key is valid

### Connection Problems
- Verify backend is running on port 5000
- Check frontend is running on port 3000
- Ensure no firewall blocking connections

## 📝 License

This project is open source and available under the MIT License.
