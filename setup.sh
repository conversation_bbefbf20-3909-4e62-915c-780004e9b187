#!/bin/bash

echo "🧠 Setting up AI Personality Training Voice Chat..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed. Please install Python 3 first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed. Please install Node.js first."
    exit 1
fi

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Create database directory
echo "🗄️ Setting up database..."
mkdir -p database

echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Copy .env.example to .env and add your API keys:"
echo "   - GEMINI_API_KEY (from https://makersuite.google.com/app/apikey)"
echo "   - ELEVENLABS_API_KEY (from https://elevenlabs.io/)"
echo ""
echo "2. Run the application:"
echo "   Backend:  python3 main.py"
echo "   Frontend: npm run dev"
echo ""
echo "3. Open http://localhost:3000 in your browser"
echo ""
echo "🎯 For voice-to-voice chat:"
echo "   - Click the wave button to start listening"
echo "   - Speak naturally - the AI will respond with voice automatically"
echo "   - No need to press record/stop buttons!"
