import React, { useRef, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Sphere, Text } from '@react-three/drei'
import * as THREE from 'three'

interface AvatarDisplayProps {
  state: 'idle' | 'listening' | 'speaking' | 'thinking'
  isConnected: boolean
  isPlaying: boolean
}

// Animated Avatar Sphere Component
const AnimatedAvatar: React.FC<{ state: string; isPlaying: boolean }> = ({ state, isPlaying }) => {
  const meshRef = useRef<THREE.Mesh>(null!)
  const materialRef = useRef<THREE.MeshStandardMaterial>(null!)
  const outerRingRef = useRef<THREE.Mesh>(null!)

  useFrame((frameState, delta) => {
    if (meshRef.current) {
      // Smooth rotation animation
      meshRef.current.rotation.y += delta * 0.3

      // Scale animation based on state
      if (state === 'speaking' || isPlaying) {
        const scale = 1 + Math.sin(frameState.clock.elapsedTime * 10) * 0.15
        meshRef.current.scale.setScalar(scale)
      } else if (state === 'listening') {
        const scale = 1 + Math.sin(frameState.clock.elapsedTime * 6) * 0.08
        meshRef.current.scale.setScalar(scale)
      } else if (state === 'thinking') {
        meshRef.current.rotation.x += delta * 0.2
        const scale = 1 + Math.sin(frameState.clock.elapsedTime * 4) * 0.05
        meshRef.current.scale.setScalar(scale)
      } else {
        // Idle state - gentle breathing
        const scale = 1 + Math.sin(frameState.clock.elapsedTime * 1.5) * 0.03
        meshRef.current.scale.setScalar(scale)
      }
    }

    // Outer ring animation
    if (outerRingRef.current) {
      outerRingRef.current.rotation.y -= delta * 0.2
      outerRingRef.current.rotation.z += delta * 0.1
    }

    // Color animation based on state
    if (materialRef.current) {
      switch (state) {
        case 'listening':
          materialRef.current.color.setHSL(0.3, 0.9, 0.7) // Vibrant green
          materialRef.current.emissive.setHSL(0.3, 0.5, 0.1)
          break
        case 'speaking':
          materialRef.current.color.setHSL(0.6, 0.9, 0.8) // Bright blue
          materialRef.current.emissive.setHSL(0.6, 0.6, 0.15)
          break
        case 'thinking':
          materialRef.current.color.setHSL(0.1, 0.9, 0.7) // Warm orange
          materialRef.current.emissive.setHSL(0.1, 0.7, 0.1)
          break
        default:
          materialRef.current.color.setHSL(0.75, 0.8, 0.8) // Soft purple
          materialRef.current.emissive.setHSL(0.75, 0.4, 0.05)
      }
    }
  })

  return (
    <group>
      {/* Main avatar sphere */}
      <Sphere ref={meshRef} args={[1.2, 64, 64]}>
        <meshStandardMaterial
          ref={materialRef}
          color="#A855F7"
          roughness={0.1}
          metalness={0.3}
          transparent
          opacity={0.9}
        />
      </Sphere>
      
      {/* Outer glow ring */}
      <Sphere ref={outerRingRef} args={[1.6, 32, 32]}>
        <meshBasicMaterial
          color="#8B5CF6"
          transparent
          opacity={0.15}
          side={THREE.BackSide}
        />
      </Sphere>

      {/* Inner core */}
      <Sphere args={[0.8, 32, 32]}>
        <meshBasicMaterial
          color="#C084FC"
          transparent
          opacity={0.3}
        />
      </Sphere>

      {/* Animated particles around avatar */}
      <group>
        {[...Array(8)].map((_, i) => {
          const angle = (i / 8) * Math.PI * 2
          const radius = 2.5
          return (
            <Sphere
              key={i}
              position={[
                Math.cos(angle) * radius,
                Math.sin(angle * 0.5) * 0.5,
                Math.sin(angle) * radius
              ]}
              args={[0.05, 8, 8]}
            >
              <meshBasicMaterial
                color="#DDD6FE"
                transparent
                opacity={0.6}
              />
            </Sphere>
          )
        })}
      </group>
    </group>
  )
}

// Floating particles for ambient effect
const FloatingParticles: React.FC = () => {
  const particlesRef = useRef<THREE.Points>(null!)
  
  useFrame((state, delta) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y += delta * 0.05
      particlesRef.current.rotation.x += delta * 0.02
    }
  })

  const particleCount = 100
  const positions = new Float32Array(particleCount * 3)
  
  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 15
    positions[i * 3 + 1] = (Math.random() - 0.5) * 15
    positions[i * 3 + 2] = (Math.random() - 0.5) * 15
  }

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#E0E7FF"
        size={0.03}
        transparent
        opacity={0.4}
      />
    </points>
  )
}

const AvatarDisplay: React.FC<AvatarDisplayProps> = ({ state, isConnected, isPlaying }) => {
  const getStateText = () => {
    switch (state) {
      case 'listening': return 'Listening to you...'
      case 'speaking': return 'Speaking...'
      case 'thinking': return 'Processing...'
      default: return 'Ready to chat'
    }
  }

  const getStateColor = () => {
    switch (state) {
      case 'listening': return 'text-green-300'
      case 'speaking': return 'text-blue-300'
      case 'thinking': return 'text-orange-300'
      default: return 'text-purple-300'
    }
  }

  const getStateIcon = () => {
    switch (state) {
      case 'listening': return '🎤'
      case 'speaking': return '🗣️'
      case 'thinking': return '🧠'
      default: return '✨'
    }
  }

  return (
    <div className="w-full h-full flex flex-col items-center justify-center">
      {/* 3D Avatar Canvas */}
      <div className="w-72 h-72 mb-6 relative">
        <Canvas 
          camera={{ position: [0, 0, 5], fov: 50 }}
          className="rounded-3xl overflow-hidden"
        >
          <ambientLight intensity={0.4} />
          <pointLight position={[10, 10, 10]} intensity={1.2} color="#8B5CF6" />
          <pointLight position={[-10, -10, -10]} intensity={0.8} color="#EC4899" />
          <pointLight position={[0, 10, -10]} intensity={0.6} color="#3B82F6" />
          
          <AnimatedAvatar state={state} isPlaying={isPlaying} />
          <FloatingParticles />
          
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            autoRotate={state === 'idle'}
            autoRotateSpeed={0.3}
            enableDamping
            dampingFactor={0.05}
          />
        </Canvas>
        
        {/* Glow effect overlay */}
        <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-500/10 to-blue-500/10 pointer-events-none animate-glow" />
      </div>

      {/* Status Display */}
      <div className="text-center space-y-4">
        <div className="glass-panel rounded-2xl px-6 py-3 inline-flex items-center gap-3">
          <span className="text-2xl">{getStateIcon()}</span>
          <div>
            <h3 className={`text-lg font-semibold ${getStateColor()}`}>
              {getStateText()}
            </h3>
            {!isConnected && (
              <p className="text-sm text-red-300 mt-1">
                Connection lost - reconnecting...
              </p>
            )}
          </div>
        </div>

        {/* Visual indicators */}
        <div className="flex items-center justify-center space-x-6">
          {/* Listening indicator */}
          {state === 'listening' && (
            <div className="glass-panel rounded-2xl p-3 flex items-center space-x-2">
              <div className="flex space-x-1">
                {[...Array(4)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 bg-green-400 rounded-full animate-pulse"
                    style={{
                      height: `${Math.random() * 20 + 10}px`,
                      animationDelay: `${i * 0.2}s`,
                      animationDuration: '1s'
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Speaking indicator */}
          {(state === 'speaking' || isPlaying) && (
            <div className="glass-panel rounded-2xl p-3 flex items-center space-x-2">
              <div className="flex space-x-1">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 bg-blue-400 rounded-full animate-bounce"
                    style={{
                      height: `${Math.random() * 24 + 8}px`,
                      animationDelay: `${i * 0.1}s`,
                      animationDuration: '0.8s'
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Thinking indicator */}
          {state === 'thinking' && (
            <div className="glass-panel rounded-2xl p-3 flex items-center space-x-2">
              <div className="flex space-x-1">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="w-3 h-3 bg-orange-400 rounded-full animate-bounce"
                    style={{
                      animationDelay: `${i * 0.3}s`,
                      animationDuration: '1.2s'
                    }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AvatarDisplay

