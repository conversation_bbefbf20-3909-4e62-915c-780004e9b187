import React, { useState, useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import { Button } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Mic, MicOff, Send, Volume2, VolumeX } from 'lucide-react'
import AvatarDisplay from './components/AvatarDisplay'
import ChatHistory from './components/ChatHistory'
import VoiceControls from './components/VoiceControls'
import PersonalityManager from './PersonalityManager'
import './App.css'

interface Message {
  id: number
  content: string
  message_type: 'user' | 'assistant' | 'system'
  created_at: string
  audio_url?: string
  metadata?: any
}

interface Conversation {
  id: number
  title: string
  messages: Message[]
}

const App: React.FC = () => {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [conversation, setConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isListening, setIsListening] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [avatarState, setAvatarState] = useState<'idle' | 'listening' | 'speaking' | 'thinking'>('idle')
  const [isLoading, setIsLoading] = useState(false)
  const [showPersonalityManager, setShowPersonalityManager] = useState(false)

  const audioRef = useRef<HTMLAudioElement>(null)

  // Initialize socket connection
  useEffect(() => {
    const newSocket = io('http://localhost:5000', {
      transports: ['websocket']
    })

    newSocket.on('connect', () => {
      console.log('Connected to server')
      setIsConnected(true)
    })

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server')
      setIsConnected(false)
    })

    newSocket.on('message_received', (data: { message: Message, type: string }) => {
      setMessages(prev => [...prev, data.message])
      if (data.type === 'assistant') {
        setAvatarState('speaking')
      }
    })

    newSocket.on('voice_response', (data: { audio_data: string, message_id: number, format: string }) => {
      if (!isMuted && audioRef.current) {
        const audioBlob = new Blob([Uint8Array.from(atob(data.audio_data), c => c.charCodeAt(0))], { type: 'audio/mpeg' })
        const audioUrl = URL.createObjectURL(audioBlob)
        audioRef.current.src = audioUrl
        audioRef.current.play()
        setIsPlaying(true)
        setAvatarState('speaking')
      }
    })

    newSocket.on('transcription_error', (data: { error: string, confidence: number }) => {
      console.warn('Transcription error:', data.error)
      setAvatarState('idle')
    })

    newSocket.on('command_executed', (data: any) => {
      console.log('Command executed:', data)
    })

    newSocket.on('error', (error: any) => {
      console.error('Socket error:', error)
      setAvatarState('idle')
    })

    setSocket(newSocket)

    return () => {
      newSocket.close()
    }
  }, [isMuted])

  // Create initial conversation
  useEffect(() => {
    if (socket && isConnected && !conversation) {
      createConversation()
    }
  }, [socket, isConnected, conversation])

  const createConversation = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/chat/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'AI Voice Chat',
          user_id: 1
        })
      })
      
      const data = await response.json()
      if (data.success) {
        setConversation(data.conversation)
        // Join the conversation room
        socket?.emit('join_conversation', {
          conversation_id: data.conversation.id,
          user_id: 1
        })
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
    }
  }

  const sendMessage = async (message: string, generateVoice = false) => {
    if (!socket || !conversation || !message.trim()) return

    setIsLoading(true)
    setAvatarState('thinking')

    socket.emit('send_message', {
      conversation_id: conversation.id,
      content: message,
      message_type: 'user',
      generate_voice: generateVoice
    })

    setInputMessage('')
    setIsLoading(false)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    sendMessage(inputMessage, true)
  }

  const handleVoiceDetected = (audioBlob: Blob) => {
    if (!socket || !conversation) return

    setAvatarState('thinking')

    // Convert audio blob to base64
    const reader = new FileReader()
    reader.onloadend = () => {
      const base64Audio = reader.result as string
      const base64Data = base64Audio.split(',')[1] // Remove data:audio/wav;base64, prefix

      socket.emit('voice_audio', {
        conversation_id: conversation.id,
        audio_data: base64Data
      })
    }
    reader.readAsDataURL(audioBlob)
  }

  const toggleListening = () => {
    setIsListening(!isListening)
    if (!isListening) {
      setAvatarState('listening')
    } else {
      setAvatarState('idle')
    }
  }

  const executeCommand = (command: string) => {
    if (!socket || !conversation) return

    socket.emit('execute_command', {
      conversation_id: conversation.id,
      command: command,
      context: {}
    })
  }

  const handleAudioEnded = () => {
    setIsPlaying(false)
    setAvatarState('idle')
  }

  return (
    <div className="min-h-screen gradient-bg flex p-6 gap-6">
      {/* Left Panel - Avatar Display */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className="floating-panel w-full max-w-2xl mb-8">
          <div className="glass-card">
            <div className="text-center mb-6">
              <div className="flex items-center justify-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-white/90">Voice AI Assistant</h1>
                <button
                  onClick={() => setShowPersonalityManager(true)}
                  className="glass-button-secondary p-2 hover:bg-blue-500/20"
                  title="Personality Manager"
                >
                  🧠
                </button>
              </div>
              <p className="text-white/70 text-sm">
                {isListening ? 'Listening for your voice...' :
                 isPlaying ? 'AI is speaking...' :
                 'Click the wave button to start talking'}
              </p>
            </div>
            
            <div className="flex items-center justify-center h-80 avatar-glow">
              <AvatarDisplay 
                state={avatarState}
                isConnected={isConnected}
                isPlaying={isPlaying}
              />
            </div>
          </div>
        </div>

        {/* Voice Controls */}
        <div className="glass-panel rounded-3xl p-6 mb-6">
          <VoiceControls
            isListening={isListening}
            isPlaying={isPlaying}
            isMuted={isMuted}
            onVoiceDetected={handleVoiceDetected}
            onToggleMute={() => setIsMuted(!isMuted)}
            onToggleListening={toggleListening}
          />
        </div>

        {/* Connection Status */}
        <div className={`connection-indicator ${
          isConnected ? 'status-connected' : 'status-disconnected'
        }`}>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'
            }`} />
            <span className="text-sm font-medium">
              {isConnected ? 'Connected' : 'Reconnecting...'}
            </span>
          </div>
        </div>
      </div>

      {/* Right Panel - Chat Interface */}
      <div className="w-96 flex flex-col">
        <div className="glass-panel rounded-3xl flex-1 flex flex-col overflow-hidden">
          {/* Chat Header */}
          <div className="p-6 border-b border-white/10">
            <h2 className="text-xl font-semibold text-white/90">Chat History</h2>
            <p className="text-white/60 text-sm mt-1">Conversation with AI</p>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 overflow-hidden">
            <ChatHistory 
              messages={messages}
              isLoading={isLoading}
            />
          </div>

          {/* Message Input - Made less prominent for voice-first experience */}
          <div className="p-6 border-t border-white/10">
            <div className="text-center mb-4">
              <p className="text-white/60 text-xs mb-3">Or type a message if needed</p>
              <form onSubmit={handleSubmit} className="flex gap-2">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Type here..."
                  className="glass-input flex-1 text-white placeholder:text-white/40 text-sm py-2"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={!inputMessage.trim() || isLoading}
                  className="glass-button-secondary p-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-3 h-3" />
                </button>
              </form>
            </div>

            {/* Personality Training Suggestions */}
            <div className="text-center">
              <p className="text-white/50 text-xs mb-2">Try saying:</p>
              <div className="flex flex-wrap gap-1 justify-center">
                <span className="glass-button-secondary text-xs py-1 px-2 cursor-default">
                  "Set personality to..."
                </span>
                <span className="glass-button-secondary text-xs py-1 px-2 cursor-default">
                  "Show personality"
                </span>
                <span className="glass-button-secondary text-xs py-1 px-2 cursor-default">
                  "Training mode on"
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hidden audio element for playing responses */}
      <audio
        ref={audioRef}
        onEnded={handleAudioEnded}
        style={{ display: 'none' }}
      />

      {/* Personality Manager Modal */}
      <PersonalityManager
        socket={socket}
        isVisible={showPersonalityManager}
        onClose={() => setShowPersonalityManager(false)}
      />
    </div>
  )
}

export default App

