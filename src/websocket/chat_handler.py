from flask import request
from flask_socketio import emit, join_room, leave_room, disconnect
from src.services.ai_service import AIService
from src.services.voice_service import VoiceService
from src.services.agent_service import AgentService
from src.models.user import db
from src.models.conversation import Conversation
from src.models.message import Message
import logging
import base64
import json

# Initialize services
ai_service = AIService()
voice_service = VoiceService()
agent_service = AgentService()

# Store active connections
active_connections = {}

def register_chat_handlers(socketio):
    """Register all WebSocket event handlers"""
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        try:
            logging.info(f"Client connected: {request.sid}")
            emit('connected', {'status': 'connected', 'sid': request.sid})
        except Exception as e:
            logging.error(f"Error in connect handler: {str(e)}")
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        try:
            logging.info(f"Client disconnected: {request.sid}")
            # Clean up any active connections
            if request.sid in active_connections:
                del active_connections[request.sid]
        except Exception as e:
            logging.error(f"Error in disconnect handler: {str(e)}")
    
    @socketio.on('join_conversation')
    def handle_join_conversation(data):
        """Handle joining a conversation room"""
        try:
            conversation_id = data.get('conversation_id')
            user_id = data.get('user_id', 1)
            
            if not conversation_id:
                emit('error', {'message': 'Conversation ID is required'})
                return
            
            # Join the conversation room
            join_room(f"conversation_{conversation_id}")
            
            # Store connection info
            active_connections[request.sid] = {
                'conversation_id': conversation_id,
                'user_id': user_id
            }
            
            emit('conversation_joined', {
                'conversation_id': conversation_id,
                'status': 'joined'
            })
            
            logging.info(f"Client {request.sid} joined conversation {conversation_id}")
            
        except Exception as e:
            logging.error(f"Error joining conversation: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('leave_conversation')
    def handle_leave_conversation(data):
        """Handle leaving a conversation room"""
        try:
            conversation_id = data.get('conversation_id')
            
            if conversation_id:
                leave_room(f"conversation_{conversation_id}")
                emit('conversation_left', {
                    'conversation_id': conversation_id,
                    'status': 'left'
                })
            
            # Clean up connection info
            if request.sid in active_connections:
                del active_connections[request.sid]
                
        except Exception as e:
            logging.error(f"Error leaving conversation: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('send_message')
    def handle_send_message(data):
        """Handle sending a text message"""
        try:
            conversation_id = data.get('conversation_id')
            content = data.get('content', '')
            message_type = data.get('message_type', 'user')
            
            if not conversation_id or not content:
                emit('error', {'message': 'Conversation ID and content are required'})
                return
            
            # Save user message
            user_message = Message(
                conversation_id=conversation_id,
                content=content,
                message_type=message_type
            )
            db.session.add(user_message)
            
            # Emit user message to room
            socketio.emit('message_received', {
                'message': user_message.to_dict(),
                'type': 'user'
            }, room=f"conversation_{conversation_id}")
            
            # Generate AI response
            try:
                ai_response = ai_service.generate_response(content, conversation_id)
                
                # Save AI message
                ai_message = Message(
                    conversation_id=conversation_id,
                    content=ai_response,
                    message_type='assistant'
                )
                db.session.add(ai_message)
                
                # Update conversation timestamp
                conversation = Conversation.query.get(conversation_id)
                if conversation:
                    conversation.updated_at = db.func.current_timestamp()
                
                db.session.commit()
                
                # Emit AI response to room
                socketio.emit('message_received', {
                    'message': ai_message.to_dict(),
                    'type': 'assistant'
                }, room=f"conversation_{conversation_id}")
                
                # Generate voice response if requested
                generate_voice = data.get('generate_voice', False)
                if generate_voice:
                    try:
                        audio_data = voice_service.synthesize_speech(ai_response)
                        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                        
                        socketio.emit('voice_response', {
                            'audio_data': audio_base64,
                            'message_id': ai_message.id,
                            'format': 'mp3'
                        }, room=f"conversation_{conversation_id}")
                        
                    except Exception as voice_error:
                        logging.error(f"Voice generation error: {str(voice_error)}")
                        socketio.emit('error', {
                            'message': 'Failed to generate voice response'
                        }, room=f"conversation_{conversation_id}")
                
            except Exception as ai_error:
                logging.error(f"AI response error: {str(ai_error)}")
                db.session.commit()  # Still save user message
                
                socketio.emit('error', {
                    'message': 'Failed to generate AI response'
                }, room=f"conversation_{conversation_id}")
                
        except Exception as e:
            logging.error(f"Error handling message: {str(e)}")
            db.session.rollback()
            emit('error', {'message': str(e)})
    
    @socketio.on('start_voice_recording')
    def handle_start_voice_recording(data):
        """Handle start of voice recording"""
        try:
            conversation_id = data.get('conversation_id')
            
            emit('recording_started', {
                'conversation_id': conversation_id,
                'status': 'recording'
            })
            
        except Exception as e:
            logging.error(f"Error starting voice recording: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('voice_audio')
    def handle_voice_audio(data):
        """Handle complete voice audio for transcription"""
        try:
            conversation_id = data.get('conversation_id')
            audio_data = data.get('audio_data')  # Base64 encoded audio

            if not conversation_id or not audio_data:
                emit('error', {'message': 'Missing conversation_id or audio_data'})
                return

            # Decode base64 audio data
            import base64
            audio_bytes = base64.b64decode(audio_data)

            # Transcribe audio to text
            transcription_result = voice_service.transcribe_audio(audio_bytes)

            if transcription_result['success'] and transcription_result['text'].strip():
                # Process the transcribed text as a regular message
                user_message = Message(
                    conversation_id=conversation_id,
                    content=transcription_result['text'],
                    message_type='user'
                )
                db.session.add(user_message)
                db.session.commit()

                # Emit the user message
                socketio.emit('message_received', {
                    'message': {
                        'id': user_message.id,
                        'content': user_message.content,
                        'message_type': user_message.message_type,
                        'created_at': user_message.created_at.isoformat()
                    },
                    'type': 'user'
                }, room=f"conversation_{conversation_id}")

                # Generate AI response
                ai_response = ai_service.generate_response(transcription_result['text'], conversation_id)

                ai_message = Message(
                    conversation_id=conversation_id,
                    content=ai_response,
                    message_type='assistant'
                )
                db.session.add(ai_message)
                db.session.commit()

                # Emit AI response
                socketio.emit('message_received', {
                    'message': {
                        'id': ai_message.id,
                        'content': ai_message.content,
                        'message_type': ai_message.message_type,
                        'created_at': ai_message.created_at.isoformat()
                    },
                    'type': 'assistant'
                }, room=f"conversation_{conversation_id}")

                # Generate voice response automatically
                try:
                    audio_data = voice_service.synthesize_speech(ai_response)
                    audio_base64 = base64.b64encode(audio_data).decode('utf-8')

                    socketio.emit('voice_response', {
                        'audio_data': audio_base64,
                        'message_id': ai_message.id,
                        'format': 'mp3'
                    }, room=f"conversation_{conversation_id}")

                except Exception as voice_error:
                    logging.error(f"Voice generation error: {str(voice_error)}")

            else:
                # Emit transcription error
                emit('transcription_error', {
                    'error': transcription_result.get('error', 'Could not understand audio'),
                    'confidence': transcription_result.get('confidence', 0.0)
                })

        except Exception as e:
            logging.error(f"Error handling voice audio: {str(e)}")
            emit('error', {'message': str(e)})

    @socketio.on('voice_chunk')
    def handle_voice_chunk(data):
        """Handle streaming voice data chunks (deprecated - use voice_audio instead)"""
        try:
            conversation_id = data.get('conversation_id')
            audio_chunk = data.get('audio_chunk')

            emit('voice_chunk_received', {
                'conversation_id': conversation_id,
                'status': 'received'
            })

        except Exception as e:
            logging.error(f"Error handling voice chunk: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('stop_voice_recording')
    def handle_stop_voice_recording(data):
        """Handle end of voice recording"""
        try:
            conversation_id = data.get('conversation_id')
            
            # In a full implementation, this would process the complete audio
            # and convert it to text, then handle as a regular message
            
            emit('recording_stopped', {
                'conversation_id': conversation_id,
                'status': 'stopped',
                'message': 'Voice-to-text processing coming soon'
            })
            
        except Exception as e:
            logging.error(f"Error stopping voice recording: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('execute_command')
    def handle_execute_command(data):
        """Handle natural language command execution"""
        try:
            conversation_id = data.get('conversation_id')
            command = data.get('command', '')
            context = data.get('context', {})
            
            if not command:
                emit('error', {'message': 'Command is required'})
                return
            
            # Emit command execution start
            emit('command_progress', {
                'status': 'started',
                'command': command,
                'message': 'Processing your command...'
            })
            
            # Execute the command
            result = agent_service.execute_command(command, context, conversation_id)
            
            # Save command as a message
            command_message = Message(
                conversation_id=conversation_id,
                content=command,
                message_type='user',
                message_metadata={'type': 'command'}
            )
            db.session.add(command_message)
            
            # Save result as assistant message
            result_content = result.get('message', 'Command executed')
            if result.get('content'):
                result_content += f"\n\n{result['content']}"
            
            result_message = Message(
                conversation_id=conversation_id,
                content=result_content,
                message_type='assistant',
                message_metadata={'type': 'command_result', 'command_result': result}
            )
            db.session.add(result_message)
            
            db.session.commit()
            
            # Emit command execution result
            emit('command_executed', {
                'result': result,
                'command': command,
                'status': 'completed'
            })
            
            # Also emit as regular messages
            socketio.emit('message_received', {
                'message': command_message.to_dict(),
                'type': 'user'
            }, room=f"conversation_{conversation_id}")
            
            socketio.emit('message_received', {
                'message': result_message.to_dict(),
                'type': 'assistant'
            }, room=f"conversation_{conversation_id}")
            
        except Exception as e:
            logging.error(f"Error executing command: {str(e)}")
            emit('command_executed', {
                'result': {'success': False, 'error': str(e)},
                'command': command,
                'status': 'failed'
            })
            emit('error', {'message': str(e)})
    
    @socketio.on('generate_voice')
    def handle_generate_voice(data):
        """Handle voice generation request"""
        try:
            text = data.get('text', '')
            voice_id = data.get('voice_id', None)
            
            if not text:
                emit('error', {'message': 'Text is required for voice generation'})
                return
            
            # Generate voice
            audio_data = voice_service.synthesize_speech(text, voice_id)
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            
            emit('voice_generated', {
                'audio_data': audio_base64,
                'text': text,
                'format': 'mp3'
            })
            
        except Exception as e:
            logging.error(f"Error generating voice: {str(e)}")
            emit('error', {'message': str(e)})
    
    return socketio

