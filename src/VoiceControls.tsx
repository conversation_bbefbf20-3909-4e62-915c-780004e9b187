import React from 'react'
import { But<PERSON> } from '@/components/ui/button.jsx'
import { Mi<PERSON>, MicOff, Volume2, VolumeX, Square, Radio } from 'lucide-react'

interface VoiceControlsProps {
  isRecording: boolean
  isPlaying: boolean
  isMuted: boolean
  onStartRecording: () => void
  onStopRecording: () => void
  onToggleMute: () => void
}

const VoiceControls: React.FC<VoiceControlsProps> = ({
  isRecording,
  isPlaying,
  isMuted,
  onStartRecording,
  onStopRecording,
  onToggleMute
}) => {
  return (
    <div className="flex items-center justify-center space-x-6">
      {/* Recording Button */}
      <div className="relative">
        <button
          className={`w-20 h-20 rounded-3xl transition-all duration-300 transform hover:scale-105 active:scale-95 ${
            isRecording 
              ? 'glass-button voice-control-active shadow-red-500/50' 
              : 'glass-button-primary shadow-blue-500/30'
          } flex items-center justify-center group`}
          onClick={isRecording ? onStopRecording : onStartRecording}
        >
          {isRecording ? (
            <Square className="w-8 h-8 text-red-300 group-hover:text-red-200 transition-colors" />
          ) : (
            <Mic className="w-8 h-8 text-blue-300 group-hover:text-blue-200 transition-colors" />
          )}
        </button>
        
        {/* Recording indicator rings */}
        {isRecording && (
          <>
            <div className="absolute inset-0 rounded-3xl border-2 border-red-400/50 animate-ping" />
            <div className="absolute inset-0 rounded-3xl border border-red-400/30 animate-pulse" />
          </>
        )}
      </div>

      {/* Mute Button */}
      <button
        className={`w-16 h-16 rounded-2xl transition-all duration-300 transform hover:scale-105 active:scale-95 ${
          isMuted 
            ? 'glass-button bg-red-500/20 border-red-400/30 shadow-red-500/25' 
            : 'glass-button-secondary shadow-gray-500/20'
        } flex items-center justify-center group`}
        onClick={onToggleMute}
      >
        {isMuted ? (
          <VolumeX className="w-6 h-6 text-red-300 group-hover:text-red-200 transition-colors" />
        ) : (
          <Volume2 className="w-6 h-6 text-gray-300 group-hover:text-gray-200 transition-colors" />
        )}
      </button>

      {/* Status Indicators */}
      <div className="flex flex-col items-center space-y-3">
        {/* Playing indicator */}
        {isPlaying && (
          <div className="glass-panel rounded-2xl px-4 py-2 flex items-center space-x-2 animate-in slide-in-from-right-2">
            <div className="flex space-x-1">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="w-1 bg-blue-400 rounded-full animate-bounce"
                  style={{
                    height: `${Math.random() * 16 + 8}px`,
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: '0.6s'
                  }}
                />
              ))}
            </div>
            <span className="text-xs text-blue-300 font-medium">Playing</span>
          </div>
        )}
        
        {/* Recording indicator */}
        {isRecording && (
          <div className="glass-panel rounded-2xl px-4 py-2 flex items-center space-x-2 animate-in slide-in-from-right-2">
            <Radio className="w-4 h-4 text-red-400 animate-pulse" />
            <span className="text-xs text-red-300 font-medium">Recording</span>
          </div>
        )}
        
        {/* Muted indicator */}
        {isMuted && (
          <div className="glass-panel rounded-2xl px-4 py-2 flex items-center space-x-2 animate-in slide-in-from-right-2">
            <div className="w-2 h-2 bg-gray-400 rounded-full" />
            <span className="text-xs text-gray-300 font-medium">Muted</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default VoiceControls

