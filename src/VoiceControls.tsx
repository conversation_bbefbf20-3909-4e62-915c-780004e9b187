import React, { useEffect, useRef, useState } from 'react'
import { Volume2, VolumeX, Radio, Waves } from 'lucide-react'

interface VoiceControlsProps {
  isListening: boolean
  isPlaying: boolean
  isMuted: boolean
  onVoiceDetected: (audioData: Blob) => void
  onToggleMute: () => void
  onToggleListening: () => void
}

const VoiceControls: React.FC<VoiceControlsProps> = ({
  isListening,
  isPlaying,
  isMuted,
  onVoiceDetected,
  onToggleMute,
  onToggleListening
}) => {
  const [audioLevel, setAudioLevel] = useState(0)
  const [isDetectingVoice, setIsDetectingVoice] = useState(false)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const recordingChunksRef = useRef<Blob[]>([])

  // Voice Activity Detection parameters
  const SILENCE_THRESHOLD = 30 // Adjust based on environment
  const SILENCE_DURATION = 1500 // ms of silence before stopping
  const MIN_RECORDING_DURATION = 500 // minimum recording time

  useEffect(() => {
    if (isListening) {
      startVoiceDetection()
    } else {
      stopVoiceDetection()
    }

    return () => {
      stopVoiceDetection()
    }
  }, [isListening])

  const startVoiceDetection = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      streamRef.current = stream

      // Set up audio analysis
      audioContextRef.current = new AudioContext()
      analyserRef.current = audioContextRef.current.createAnalyser()
      const source = audioContextRef.current.createMediaStreamSource(stream)
      source.connect(analyserRef.current)

      analyserRef.current.fftSize = 256
      const bufferLength = analyserRef.current.frequencyBinCount
      const dataArray = new Uint8Array(bufferLength)

      // Set up MediaRecorder
      mediaRecorderRef.current = new MediaRecorder(stream)
      recordingChunksRef.current = []

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordingChunksRef.current.push(event.data)
        }
      }

      mediaRecorderRef.current.onstop = () => {
        if (recordingChunksRef.current.length > 0) {
          const audioBlob = new Blob(recordingChunksRef.current, { type: 'audio/wav' })
          onVoiceDetected(audioBlob)
          recordingChunksRef.current = []
        }
      }

      // Start monitoring audio levels
      const monitorAudio = () => {
        if (!analyserRef.current || !isListening) return

        analyserRef.current.getByteFrequencyData(dataArray)
        const average = dataArray.reduce((a, b) => a + b) / bufferLength
        setAudioLevel(average)

        // Voice activity detection
        if (average > SILENCE_THRESHOLD) {
          if (!isDetectingVoice) {
            setIsDetectingVoice(true)
            mediaRecorderRef.current?.start()
          }

          // Clear silence timeout
          if (silenceTimeoutRef.current) {
            clearTimeout(silenceTimeoutRef.current)
            silenceTimeoutRef.current = null
          }
        } else if (isDetectingVoice) {
          // Start silence timeout
          if (!silenceTimeoutRef.current) {
            silenceTimeoutRef.current = setTimeout(() => {
              setIsDetectingVoice(false)
              mediaRecorderRef.current?.stop()
              silenceTimeoutRef.current = null
            }, SILENCE_DURATION)
          }
        }

        requestAnimationFrame(monitorAudio)
      }

      monitorAudio()

    } catch (error) {
      console.error('Error starting voice detection:', error)
    }
  }

  const stopVoiceDetection = () => {
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current)
      silenceTimeoutRef.current = null
    }

    if (mediaRecorderRef.current && isDetectingVoice) {
      mediaRecorderRef.current.stop()
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }

    if (audioContextRef.current) {
      audioContextRef.current.close()
      audioContextRef.current = null
    }

    setIsDetectingVoice(false)
    setAudioLevel(0)
  }

  return (
    <div className="flex items-center justify-center space-x-6">
      {/* Voice Activity Indicator */}
      <div className="relative">
        <button
          className={`w-20 h-20 rounded-3xl transition-all duration-300 transform hover:scale-105 active:scale-95 ${
            isListening
              ? 'glass-button-primary shadow-blue-500/30'
              : 'glass-button shadow-gray-500/20'
          } flex items-center justify-center group`}
          onClick={onToggleListening}
        >
          <Waves className={`w-8 h-8 transition-colors ${
            isListening ? 'text-blue-300' : 'text-gray-400'
          }`} />
        </button>

        {/* Voice activity rings */}
        {isDetectingVoice && (
          <>
            <div className="absolute inset-0 rounded-3xl border-2 border-green-400/50 animate-ping" />
            <div className="absolute inset-0 rounded-3xl border border-green-400/30 animate-pulse" />
          </>
        )}

        {/* Audio level indicator */}
        {isListening && (
          <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
            <div className="w-16 h-1 bg-gray-600 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-green-400 to-blue-400 transition-all duration-100"
                style={{ width: `${Math.min(audioLevel * 2, 100)}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Mute Button */}
      <button
        className={`w-16 h-16 rounded-2xl transition-all duration-300 transform hover:scale-105 active:scale-95 ${
          isMuted
            ? 'glass-button bg-red-500/20 border-red-400/30 shadow-red-500/25'
            : 'glass-button-secondary shadow-gray-500/20'
        } flex items-center justify-center group`}
        onClick={onToggleMute}
      >
        {isMuted ? (
          <VolumeX className="w-6 h-6 text-red-300 group-hover:text-red-200 transition-colors" />
        ) : (
          <Volume2 className="w-6 h-6 text-gray-300 group-hover:text-gray-200 transition-colors" />
        )}
      </button>

      {/* Status Indicators */}
      <div className="flex flex-col items-center space-y-3">
        {/* Playing indicator */}
        {isPlaying && (
          <div className="glass-panel rounded-2xl px-4 py-2 flex items-center space-x-2 animate-in slide-in-from-right-2">
            <div className="flex space-x-1">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="w-1 bg-blue-400 rounded-full animate-bounce"
                  style={{
                    height: `${Math.random() * 16 + 8}px`,
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: '0.6s'
                  }}
                />
              ))}
            </div>
            <span className="text-xs text-blue-300 font-medium">AI Speaking</span>
          </div>
        )}

        {/* Voice detection indicator */}
        {isDetectingVoice && (
          <div className="glass-panel rounded-2xl px-4 py-2 flex items-center space-x-2 animate-in slide-in-from-right-2">
            <Radio className="w-4 h-4 text-green-400 animate-pulse" />
            <span className="text-xs text-green-300 font-medium">Listening</span>
          </div>
        )}

        {/* Listening indicator */}
        {isListening && !isDetectingVoice && (
          <div className="glass-panel rounded-2xl px-4 py-2 flex items-center space-x-2 animate-in slide-in-from-right-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
            <span className="text-xs text-blue-300 font-medium">Ready to Listen</span>
          </div>
        )}

        {/* Muted indicator */}
        {isMuted && (
          <div className="glass-panel rounded-2xl px-4 py-2 flex items-center space-x-2 animate-in slide-in-from-right-2">
            <div className="w-2 h-2 bg-gray-400 rounded-full" />
            <span className="text-xs text-gray-300 font-medium">Muted</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default VoiceControls

