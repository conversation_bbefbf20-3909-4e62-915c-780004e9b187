import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Save, Load, Trash2, <PERSON>r, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'

interface PersonalityProfile {
  name: string
  prompt: string
  created_at: string
  training_mode: boolean
}

interface PersonalityManagerProps {
  socket: any
  isVisible: boolean
  onClose: () => void
}

const PersonalityManager: React.FC<PersonalityManagerProps> = ({
  socket,
  isVisible,
  onClose
}) => {
  const [personalities, setPersonalities] = useState<PersonalityProfile[]>([])
  const [currentPersonality, setCurrentPersonality] = useState('')
  const [newPersonalityName, setNewPersonalityName] = useState('')
  const [newPersonalityPrompt, setNewPersonalityPrompt] = useState('')
  const [trainingMode, setTrainingMode] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (socket && isVisible) {
      // Request current personality and saved profiles
      socket.emit('execute_command', {
        command: 'show personality',
        context: {}
      })
      
      socket.emit('execute_command', {
        command: 'list personalities',
        context: {}
      })
    }
  }, [socket, isVisible])

  useEffect(() => {
    if (socket) {
      socket.on('command_executed', (data: any) => {
        if (data.intent === 'show_personality') {
          setCurrentPersonality(data.personality || '')
          setTrainingMode(data.training_mode || false)
        } else if (data.intent === 'list_personalities') {
          if (data.personalities) {
            const profileList = Object.entries(data.personalities).map(([name, profile]: [string, any]) => ({
              name,
              prompt: profile.prompt,
              created_at: profile.created_at,
              training_mode: profile.training_mode
            }))
            setPersonalities(profileList)
          }
        }
      })

      return () => {
        socket.off('command_executed')
      }
    }
  }, [socket])

  const handleSetPersonality = () => {
    if (!newPersonalityPrompt.trim()) return
    
    setIsLoading(true)
    socket?.emit('execute_command', {
      command: `set personality ${newPersonalityPrompt}`,
      context: {}
    })
    
    setTimeout(() => {
      setIsLoading(false)
      setCurrentPersonality(newPersonalityPrompt)
      setNewPersonalityPrompt('')
    }, 1000)
  }

  const handleSavePersonality = () => {
    if (!newPersonalityName.trim() || !currentPersonality.trim()) return
    
    setIsLoading(true)
    socket?.emit('execute_command', {
      command: `save personality ${newPersonalityName}`,
      context: {}
    })
    
    setTimeout(() => {
      setIsLoading(false)
      setNewPersonalityName('')
      // Refresh the list
      socket?.emit('execute_command', {
        command: 'list personalities',
        context: {}
      })
    }, 1000)
  }

  const handleLoadPersonality = (name: string) => {
    setIsLoading(true)
    socket?.emit('execute_command', {
      command: `load personality ${name}`,
      context: {}
    })
    
    setTimeout(() => {
      setIsLoading(false)
      // Refresh current personality
      socket?.emit('execute_command', {
        command: 'show personality',
        context: {}
      })
    }, 1000)
  }

  const handleResetPersonality = () => {
    setIsLoading(true)
    socket?.emit('execute_command', {
      command: 'reset personality',
      context: {}
    })
    
    setTimeout(() => {
      setIsLoading(false)
      setCurrentPersonality('')
    }, 1000)
  }

  const handleToggleTraining = () => {
    const newMode = !trainingMode
    setIsLoading(true)
    socket?.emit('execute_command', {
      command: `training mode ${newMode ? 'on' : 'off'}`,
      context: {}
    })
    
    setTimeout(() => {
      setIsLoading(false)
      setTrainingMode(newMode)
    }, 1000)
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="glass-panel rounded-3xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Brain className="w-6 h-6 text-blue-400" />
              <h2 className="text-2xl font-bold text-white/90">Personality Manager</h2>
            </div>
            <button
              onClick={onClose}
              className="glass-button-secondary p-2 hover:bg-red-500/20"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Current Personality */}
            <div className="space-y-4">
              <div className="glass-card p-4">
                <div className="flex items-center gap-2 mb-3">
                  <User className="w-5 h-5 text-green-400" />
                  <h3 className="text-lg font-semibold text-white/90">Current Personality</h3>
                </div>
                
                {currentPersonality ? (
                  <div className="space-y-3">
                    <p className="text-white/70 text-sm bg-white/5 p-3 rounded-lg">
                      {currentPersonality}
                    </p>
                    <div className="flex items-center gap-2">
                      <Settings className="w-4 h-4 text-blue-400" />
                      <span className="text-sm text-white/60">
                        Training Mode: {trainingMode ? 'On' : 'Off'}
                      </span>
                      <button
                        onClick={handleToggleTraining}
                        disabled={isLoading}
                        className={`text-xs px-2 py-1 rounded ${
                          trainingMode 
                            ? 'bg-green-500/20 text-green-300' 
                            : 'bg-gray-500/20 text-gray-300'
                        }`}
                      >
                        Toggle
                      </button>
                    </div>
                    <button
                      onClick={handleResetPersonality}
                      disabled={isLoading}
                      className="glass-button-secondary text-sm py-2 px-3 w-full"
                    >
                      Reset to Default
                    </button>
                  </div>
                ) : (
                  <p className="text-white/50 text-sm">No personality set. Using default mode.</p>
                )}
              </div>

              {/* Set New Personality */}
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white/90 mb-3">Set New Personality</h3>
                <div className="space-y-3">
                  <textarea
                    value={newPersonalityPrompt}
                    onChange={(e) => setNewPersonalityPrompt(e.target.value)}
                    placeholder="Describe the personality you want the AI to embody..."
                    className="glass-input w-full h-24 text-white placeholder:text-white/50 resize-none"
                    disabled={isLoading}
                  />
                  <button
                    onClick={handleSetPersonality}
                    disabled={!newPersonalityPrompt.trim() || isLoading}
                    className="glass-button-primary w-full py-2 disabled:opacity-50"
                  >
                    Set Personality
                  </button>
                </div>
              </div>

              {/* Save Current Personality */}
              {currentPersonality && (
                <div className="glass-card p-4">
                  <h3 className="text-lg font-semibold text-white/90 mb-3">Save Current Personality</h3>
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={newPersonalityName}
                      onChange={(e) => setNewPersonalityName(e.target.value)}
                      placeholder="Enter a name for this personality..."
                      className="glass-input w-full text-white placeholder:text-white/50"
                      disabled={isLoading}
                    />
                    <button
                      onClick={handleSavePersonality}
                      disabled={!newPersonalityName.trim() || isLoading}
                      className="glass-button-primary w-full py-2 disabled:opacity-50 flex items-center justify-center gap-2"
                    >
                      <Save className="w-4 h-4" />
                      Save Personality
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Saved Personalities */}
            <div className="space-y-4">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white/90 mb-3">Saved Personalities</h3>
                
                {personalities.length > 0 ? (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {personalities.map((personality) => (
                      <div key={personality.name} className="bg-white/5 p-3 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-white/90">{personality.name}</h4>
                          <button
                            onClick={() => handleLoadPersonality(personality.name)}
                            disabled={isLoading}
                            className="glass-button-secondary text-xs py-1 px-2 flex items-center gap-1"
                          >
                            <Load className="w-3 h-3" />
                            Load
                          </button>
                        </div>
                        <p className="text-white/60 text-sm mb-2">
                          {personality.prompt.length > 100 
                            ? `${personality.prompt.substring(0, 100)}...` 
                            : personality.prompt}
                        </p>
                        <div className="text-xs text-white/40">
                          Created: {new Date(personality.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-white/50 text-sm">No saved personalities yet.</p>
                )}
              </div>
            </div>
          </div>

          {/* Help Text */}
          <div className="mt-6 glass-card p-4">
            <h3 className="text-lg font-semibold text-white/90 mb-2">How to Use</h3>
            <div className="text-white/60 text-sm space-y-1">
              <p>• <strong>Set Personality:</strong> Describe how you want the AI to behave and respond</p>
              <p>• <strong>Training Mode:</strong> When enabled, the AI is more receptive to personality adjustments</p>
              <p>• <strong>Save/Load:</strong> Store your favorite personalities for easy switching</p>
              <p>• <strong>Voice Commands:</strong> You can also use voice commands like "Set personality to be a wise mentor"</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PersonalityManager
