from flask import Blueprint, request, jsonify, Response
from src.services.voice_service import VoiceService
import logging
import base64
import io

voice_bp = Blueprint('voice', __name__)
voice_service = VoiceService()

@voice_bp.route('/synthesize', methods=['POST'])
def synthesize_speech():
    """Convert text to speech using Eleven Labs"""
    try:
        data = request.get_json()
        text = data.get('text', '')
        voice_id = data.get('voice_id', None)
        stream = data.get('stream', False)
        
        if not text:
            return jsonify({'success': False, 'error': 'Text is required'}), 400
        
        if stream:
            # Return streaming audio response
            def generate_audio():
                try:
                    for chunk in voice_service.synthesize_speech_stream(text, voice_id):
                        yield chunk
                except Exception as e:
                    logging.error(f"Error in audio streaming: {str(e)}")
                    yield b''  # End stream on error
            
            return Response(
                generate_audio(),
                mimetype='audio/mpeg',
                headers={
                    'Content-Type': 'audio/mpeg',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            )
        else:
            # Return base64 encoded audio
            audio_data = voice_service.synthesize_speech(text, voice_id)
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            
            return jsonify({
                'success': True,
                'audio_data': audio_base64,
                'format': 'mp3'
            })
            
    except Exception as e:
        logging.error(f"Error synthesizing speech: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/stream-synthesize', methods=['POST'])
def stream_synthesize():
    """Stream text-to-speech conversion"""
    try:
        data = request.get_json()
        text = data.get('text', '')
        voice_id = data.get('voice_id', None)
        
        if not text:
            return jsonify({'success': False, 'error': 'Text is required'}), 400
        
        def generate_audio_stream():
            try:
                for chunk in voice_service.synthesize_speech_stream(text, voice_id):
                    yield chunk
            except Exception as e:
                logging.error(f"Error in streaming synthesis: {str(e)}")
        
        return Response(
            generate_audio_stream(),
            mimetype='audio/mpeg',
            headers={
                'Content-Type': 'audio/mpeg',
                'Transfer-Encoding': 'chunked'
            }
        )
        
    except Exception as e:
        logging.error(f"Error in stream synthesis: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/transcribe', methods=['POST'])
def transcribe_audio():
    """Convert speech to text (placeholder for future implementation)"""
    try:
        # This would typically use a speech-to-text service
        # For now, return a placeholder response
        return jsonify({
            'success': True,
            'text': 'Speech-to-text functionality coming soon',
            'confidence': 0.0
        })
        
    except Exception as e:
        logging.error(f"Error transcribing audio: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/voices', methods=['GET'])
def get_available_voices():
    """Get list of available voices from Eleven Labs"""
    try:
        voices = voice_service.get_available_voices()
        
        return jsonify({
            'success': True,
            'voices': voices
        })
        
    except Exception as e:
        logging.error(f"Error getting voices: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/sound-effects', methods=['POST'])
def generate_sound_effects():
    """Generate sound effects using Eleven Labs"""
    try:
        data = request.get_json()
        description = data.get('description', '')
        duration = data.get('duration', 5.0)
        
        if not description:
            return jsonify({'success': False, 'error': 'Description is required'}), 400
        
        audio_data = voice_service.generate_sound_effect(description, duration)
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
        
        return jsonify({
            'success': True,
            'audio_data': audio_base64,
            'format': 'mp3',
            'description': description
        })
        
    except Exception as e:
        logging.error(f"Error generating sound effects: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/voice-settings', methods=['GET', 'POST'])
def voice_settings():
    """Get or update voice settings"""
    try:
        if request.method == 'GET':
            settings = voice_service.get_voice_settings()
            return jsonify({
                'success': True,
                'settings': settings
            })
        else:
            data = request.get_json()
            updated_settings = voice_service.update_voice_settings(data)
            return jsonify({
                'success': True,
                'settings': updated_settings
            })
            
    except Exception as e:
        logging.error(f"Error with voice settings: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

