from datetime import datetime
from src.models.user import db
import json

class PersonalityProfile(db.Model):
    """Enhanced personality profiles with learning capabilities"""
    __tablename__ = 'personality_profiles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    base_prompt = db.Column(db.Text, nullable=False)
    current_state = db.Column(db.JSON, nullable=True)  # Current personality state/traits
    learning_enabled = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    training_mode = db.Column(db.<PERSON><PERSON><PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    memories = db.relationship('PersonalityMemory', backref='personality', lazy=True, cascade='all, delete-orphan')
    training_examples = db.relationship('TrainingExample', backref='personality', lazy=True, cascade='all, delete-orphan')
    behavioral_patterns = db.relationship('BehavioralPattern', backref='personality', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'base_prompt': self.base_prompt,
            'current_state': self.current_state,
            'learning_enabled': self.learning_enabled,
            'training_mode': self.training_mode,
            'created_at': self.created_at.isoformat(),
            'last_updated': self.last_updated.isoformat(),
            'memory_count': len(self.memories),
            'training_examples_count': len(self.training_examples),
            'behavioral_patterns_count': len(self.behavioral_patterns)
        }

class PersonalityMemory(db.Model):
    """Stores all interactions and experiences for personality learning"""
    __tablename__ = 'personality_memories'
    
    id = db.Column(db.Integer, primary_key=True)
    personality_id = db.Column(db.Integer, db.ForeignKey('personality_profiles.id'), nullable=False)
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversations.id'), nullable=True)
    
    # Memory content
    user_input = db.Column(db.Text, nullable=False)
    ai_response = db.Column(db.Text, nullable=False)
    context = db.Column(db.JSON, nullable=True)  # Conversation context at time of interaction
    
    # Memory metadata
    memory_type = db.Column(db.String(50), default='conversation')  # conversation, training, correction, etc.
    importance_score = db.Column(db.Float, default=1.0)  # How important this memory is (0-10)
    emotional_context = db.Column(db.JSON, nullable=True)  # Emotional state/context
    tags = db.Column(db.JSON, nullable=True)  # Searchable tags
    
    # Learning data
    was_corrected = db.Column(db.Boolean, default=False)
    correction_feedback = db.Column(db.Text, nullable=True)
    reinforcement_score = db.Column(db.Float, default=0.0)  # Positive/negative reinforcement
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'personality_id': self.personality_id,
            'conversation_id': self.conversation_id,
            'user_input': self.user_input,
            'ai_response': self.ai_response,
            'context': self.context,
            'memory_type': self.memory_type,
            'importance_score': self.importance_score,
            'emotional_context': self.emotional_context,
            'tags': self.tags,
            'was_corrected': self.was_corrected,
            'correction_feedback': self.correction_feedback,
            'reinforcement_score': self.reinforcement_score,
            'created_at': self.created_at.isoformat()
        }

class TrainingExample(db.Model):
    """Specific training examples provided by the user"""
    __tablename__ = 'training_examples'
    
    id = db.Column(db.Integer, primary_key=True)
    personality_id = db.Column(db.Integer, db.ForeignKey('personality_profiles.id'), nullable=False)
    
    # Training content
    scenario = db.Column(db.Text, nullable=False)  # The situation/input
    expected_response = db.Column(db.Text, nullable=False)  # How personality should respond
    explanation = db.Column(db.Text, nullable=True)  # Why this response is correct
    
    # Training metadata
    category = db.Column(db.String(100), nullable=True)  # Type of training (speech_pattern, behavior, etc.)
    priority = db.Column(db.Integer, default=1)  # Training priority (1-10)
    tags = db.Column(db.JSON, nullable=True)
    
    # Learning tracking
    times_referenced = db.Column(db.Integer, default=0)
    last_used = db.Column(db.DateTime, nullable=True)
    effectiveness_score = db.Column(db.Float, default=0.0)  # How well this training works
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'personality_id': self.personality_id,
            'scenario': self.scenario,
            'expected_response': self.expected_response,
            'explanation': self.explanation,
            'category': self.category,
            'priority': self.priority,
            'tags': self.tags,
            'times_referenced': self.times_referenced,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'effectiveness_score': self.effectiveness_score,
            'created_at': self.created_at.isoformat()
        }

class BehavioralPattern(db.Model):
    """Learned behavioral patterns that emerge from training"""
    __tablename__ = 'behavioral_patterns'
    
    id = db.Column(db.Integer, primary_key=True)
    personality_id = db.Column(db.Integer, db.ForeignKey('personality_profiles.id'), nullable=False)
    
    # Pattern data
    pattern_name = db.Column(db.String(200), nullable=False)
    pattern_description = db.Column(db.Text, nullable=False)
    trigger_conditions = db.Column(db.JSON, nullable=True)  # When this pattern activates
    response_template = db.Column(db.Text, nullable=True)  # How to respond
    
    # Pattern metadata
    confidence_score = db.Column(db.Float, default=0.0)  # How confident we are in this pattern
    usage_count = db.Column(db.Integer, default=0)
    success_rate = db.Column(db.Float, default=0.0)  # How often this pattern works well
    
    # Learning data
    learned_from_examples = db.Column(db.JSON, nullable=True)  # Which training examples contributed
    reinforcement_history = db.Column(db.JSON, nullable=True)  # History of positive/negative feedback
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'personality_id': self.personality_id,
            'pattern_name': self.pattern_name,
            'pattern_description': self.pattern_description,
            'trigger_conditions': self.trigger_conditions,
            'response_template': self.response_template,
            'confidence_score': self.confidence_score,
            'usage_count': self.usage_count,
            'success_rate': self.success_rate,
            'learned_from_examples': self.learned_from_examples,
            'reinforcement_history': self.reinforcement_history,
            'created_at': self.created_at.isoformat(),
            'last_updated': self.last_updated.isoformat()
        }

class PersonalityEvolution(db.Model):
    """Tracks how personalities evolve and develop over time"""
    __tablename__ = 'personality_evolution'
    
    id = db.Column(db.Integer, primary_key=True)
    personality_id = db.Column(db.Integer, db.ForeignKey('personality_profiles.id'), nullable=False)
    
    # Evolution data
    evolution_type = db.Column(db.String(100), nullable=False)  # trait_development, speech_change, etc.
    description = db.Column(db.Text, nullable=False)
    before_state = db.Column(db.JSON, nullable=True)
    after_state = db.Column(db.JSON, nullable=True)
    
    # Evolution metadata
    trigger_event = db.Column(db.Text, nullable=True)  # What caused this evolution
    confidence = db.Column(db.Float, default=0.0)
    impact_score = db.Column(db.Float, default=0.0)  # How significant this change is
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'personality_id': self.personality_id,
            'evolution_type': self.evolution_type,
            'description': self.description,
            'before_state': self.before_state,
            'after_state': self.after_state,
            'trigger_event': self.trigger_event,
            'confidence': self.confidence,
            'impact_score': self.impact_score,
            'created_at': self.created_at.isoformat()
        }

class MemoryTag(db.Model):
    """Tags for organizing and searching memories"""
    __tablename__ = 'memory_tags'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), nullable=True)  # emotion, topic, behavior, etc.
    usage_count = db.Column(db.Integer, default=0)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'usage_count': self.usage_count,
            'created_at': self.created_at.isoformat()
        }
