from datetime import datetime
from src.models.user import db

class Message(db.Model):
    __tablename__ = 'messages'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    conversation_id = db.Column(db.Integer, db.<PERSON><PERSON>('conversations.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    message_type = db.Column(db.String(20), nullable=False)  # 'user', 'assistant', 'system'
    audio_url = db.Column(db.String(500), nullable=True)  # URL to audio file if voice message
    message_metadata = db.Column(db.JSON, nullable=True)  # Additional metadata (emotions, commands, etc.)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_processed = db.Column(db.<PERSON>, default=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'content': self.content,
            'message_type': self.message_type,
            'audio_url': self.audio_url,
            'metadata': self.message_metadata,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_processed': self.is_processed
        }

