import os
import google.generativeai as genai
from src.models.message import Message
from src.models.conversation import Conversation
import logging

class AIService:
    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        self.personality_prompt = ""
        self.base_personality = "You are a helpful AI assistant ready to be trained with specific personality traits."

        if self.api_key:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-pro')
        else:
            logging.warning("Gemini API key not found. AI features will be limited.")
            self.model = None
    
    def generate_response(self, user_message, conversation_id=None, memory_service=None):
        """Generate AI response using Gemini API with personality memory"""
        try:
            if not self.model:
                return "AI service is not properly configured. Please check your API keys."

            # Get conversation context if available
            context = self._get_conversation_context(conversation_id)

            # Get relevant memories if memory service is available
            relevant_memories = []
            behavioral_patterns = []
            if memory_service:
                relevant_memories = memory_service.get_relevant_memories(user_message, limit=5)
                behavioral_patterns = memory_service.get_behavioral_patterns()

            # Prepare enhanced prompt with context, memories, and patterns
            prompt = self._prepare_enhanced_prompt(user_message, context, relevant_memories, behavioral_patterns)

            # Generate response
            response = self.model.generate_content(prompt)

            response_text = response.text if response.text else "I'm sorry, I couldn't generate a response at the moment."

            # Store this interaction in memory if memory service is available
            if memory_service and response.text:
                memory_service.store_interaction_memory(
                    user_input=user_message,
                    ai_response=response_text,
                    conversation_id=conversation_id,
                    context={'conversation_context': context}
                )

            return response_text

        except Exception as e:
            logging.error(f"Error generating AI response: {str(e)}")
            return "I apologize, but I'm experiencing some technical difficulties. Please try again."
    
    def generate_content(self, content_type, prompt, parameters=None):
        """Generate specific content types (scripts, stories, etc.)"""
        try:
            if not self.model:
                return "AI service is not properly configured."
            
            # Prepare specialized prompts based on content type
            if content_type == 'script':
                full_prompt = self._prepare_script_prompt(prompt, parameters)
            elif content_type == 'story':
                full_prompt = self._prepare_story_prompt(prompt, parameters)
            elif content_type == 'description':
                full_prompt = self._prepare_description_prompt(prompt, parameters)
            else:
                full_prompt = prompt
            
            response = self.model.generate_content(full_prompt)
            
            if response.text:
                return response.text
            else:
                return "Unable to generate content at this time."
                
        except Exception as e:
            logging.error(f"Error generating content: {str(e)}")
            return f"Error generating {content_type}: {str(e)}"
    
    def analyze_command_intent(self, command):
        """Analyze natural language command to extract intent and parameters"""
        try:
            if not self.model:
                return {"intent": "unknown", "parameters": {}, "confidence": 0.0}
            
            prompt = f"""
            Analyze the following command and extract the intent and parameters:
            Command: "{command}"
            
            Please respond in JSON format with:
            - intent: the main action the user wants to perform
            - parameters: any specific parameters or values mentioned
            - confidence: confidence level (0.0 to 1.0)
            - explanation: brief explanation of the interpretation
            
            Focus on YouTube content creation related intents like:
            - upload_video, create_script, generate_thumbnail, analyze_performance, etc.
            """
            
            response = self.model.generate_content(prompt)
            
            if response.text:
                # Try to parse JSON response
                import json
                try:
                    result = json.loads(response.text)
                    return result
                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    return {
                        "intent": "general_query",
                        "parameters": {"query": command},
                        "confidence": 0.5,
                        "explanation": response.text
                    }
            else:
                return {"intent": "unknown", "parameters": {}, "confidence": 0.0}
                
        except Exception as e:
            logging.error(f"Error analyzing command intent: {str(e)}")
            return {"intent": "error", "parameters": {"error": str(e)}, "confidence": 0.0}
    
    def _get_conversation_context(self, conversation_id, limit=10):
        """Get recent conversation context"""
        if not conversation_id:
            return []
        
        try:
            messages = Message.query.filter_by(conversation_id=conversation_id)\
                .order_by(Message.created_at.desc())\
                .limit(limit)\
                .all()
            
            context = []
            for message in reversed(messages):  # Reverse to get chronological order
                context.append({
                    'role': 'user' if message.message_type == 'user' else 'assistant',
                    'content': message.content
                })
            
            return context
        except Exception as e:
            logging.error(f"Error getting conversation context: {str(e)}")
            return []
    
    def set_personality(self, personality_description):
        """Set the AI personality with a detailed prompt"""
        self.personality_prompt = personality_description
        logging.info(f"Personality set: {personality_description}")

    def reset_personality(self):
        """Reset to default personality"""
        self.personality_prompt = ""
        logging.info("Personality reset to default")

    def get_current_personality(self):
        """Get the current personality prompt"""
        return self.personality_prompt if self.personality_prompt else self.base_personality

    def _prepare_prompt(self, user_message, context):
        """Prepare prompt with conversation context and personality"""
        # Start with personality or base prompt
        if self.personality_prompt:
            system_prompt = f"""You must embody this personality: {self.personality_prompt}

Stay in character at all times. Respond as this personality would respond, using their speech patterns,
mannerisms, and perspective. Be consistent with this personality throughout the conversation."""
        else:
            system_prompt = self.base_personality

        if context:
            context_text = "\n".join([f"{msg['role']}: {msg['content']}" for msg in context[-5:]])  # Last 5 messages
            prompt = f"{system_prompt}\n\nConversation context:\n{context_text}\n\nUser: {user_message}\nAssistant:"
        else:
            prompt = f"{system_prompt}\n\nUser: {user_message}\nAssistant:"

        return prompt

    def _prepare_enhanced_prompt(self, user_message, context, relevant_memories=None, behavioral_patterns=None):
        """Prepare enhanced prompt with personality memory and patterns"""
        try:
            # Start with personality or base prompt
            if self.personality_prompt:
                system_prompt = f"""You must embody this personality: {self.personality_prompt}

Stay in character at all times. Respond as this personality would respond, using their speech patterns,
mannerisms, and perspective. Be consistent with this personality throughout the conversation."""
            else:
                system_prompt = self.base_personality

            # Add behavioral patterns if available
            if behavioral_patterns:
                patterns_text = "\n\nLearned Behavioral Patterns:\n"
                for pattern in behavioral_patterns[:3]:  # Top 3 patterns
                    patterns_text += f"- {pattern.pattern_name}: {pattern.pattern_description}\n"
                system_prompt += patterns_text

            # Add relevant memories if available
            if relevant_memories:
                memories_text = "\n\nRelevant Past Interactions:\n"
                for memory in relevant_memories[:3]:  # Top 3 memories
                    memories_text += f"User: {memory.user_input}\nYou: {memory.ai_response}\n---\n"
                system_prompt += memories_text

            # Add conversation context if available
            if context:
                context_text = "\n\nCurrent Conversation:\n"
                for msg in context[-5:]:  # Last 5 messages
                    role = "User" if msg['role'] == 'user' else "You"
                    context_text += f"{role}: {msg['content']}\n"
                system_prompt += context_text

            # Combine system prompt with user message
            full_prompt = f"{system_prompt}\n\nUser: {user_message}\nYou:"

            return full_prompt

        except Exception as e:
            logging.error(f"Error preparing enhanced prompt: {str(e)}")
            return self._prepare_prompt(user_message, context)
    
    def _prepare_script_prompt(self, prompt, parameters):
        """Prepare prompt for general script generation"""
        style = parameters.get('style', 'engaging and informative') if parameters else 'engaging and informative'

        return f"""
        Create a script about: {prompt}
        Style: {style}

        Please create an engaging script that covers the topic thoroughly.
        Include clear structure and natural flow.
        """
    
    def _prepare_story_prompt(self, prompt, parameters):
        """Prepare prompt for story generation"""
        genre = parameters.get('genre', 'general') if parameters else 'general'
        length = parameters.get('length', 'medium') if parameters else 'medium'
        
        return f"""
        Create an engaging story based on: {prompt}
        
        Requirements:
        - Genre: {genre}
        - Length: {length}
        - Make it suitable for video content
        - Include visual descriptions for potential scenes
        
        Focus on creating compelling narrative that would work well as video content.
        """
    
    def _prepare_description_prompt(self, prompt, parameters):
        """Prepare prompt for description generation"""
        return f"""
        Create a compelling YouTube video description for: {prompt}
        
        Include:
        - Engaging opening
        - Key points covered
        - Relevant hashtags
        - Call-to-action for likes, comments, and subscriptions
        
        Make it SEO-friendly and engaging for viewers.
        """

