import os
import requests
from elevenlabs.client import ElevenLabs
import logging
import io
import speech_recognition as sr
import tempfile
import wave

class VoiceService:
    def __init__(self):
        self.api_key = os.getenv('ELEVENLABS_API_KEY')
        self.default_voice_id = os.getenv('DEFAULT_VOICE_ID', '21m00Tcm4TlvDq8ikWAM')
        self.base_url = 'https://api.elevenlabs.io/v1'

        if self.api_key:
            self.client = ElevenLabs(api_key=self.api_key)
        else:
            logging.warning("Eleven Labs API key not found. Voice features will be limited.")
            self.client = None

        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        self.recognizer.energy_threshold = 300
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8
        self.recognizer.phrase_threshold = 0.3

        # Voice settings
        self.voice_settings = {
            'stability': 0.5,
            'similarity_boost': 0.5,
            'style': 0.0,
            'use_speaker_boost': True
        }
    
    def synthesize_speech(self, text, voice_id=None):
        """Convert text to speech and return audio data"""
        try:
            if not self.client:
                raise Exception("Voice service is not properly configured")
            
            voice_id = voice_id or self.default_voice_id
            
            # Generate audio using Eleven Labs client
            audio = self.client.text_to_speech.convert(
                voice_id=voice_id,
                text=text,
                model_id="eleven_monolingual_v1"
            )
            
            # Convert generator to bytes
            audio_bytes = b''.join(audio)
            return audio_bytes
            
        except Exception as e:
            logging.error(f"Error synthesizing speech: {str(e)}")
            raise e
    
    def synthesize_speech_stream(self, text, voice_id=None):
        """Stream text-to-speech conversion"""
        try:
            if not self.client:
                raise Exception("Voice service is not properly configured")
            
            voice_id = voice_id or self.default_voice_id
            
            # Use streaming text-to-speech
            audio_stream = self.client.text_to_speech.convert(
                voice_id=voice_id,
                text=text,
                model_id="eleven_monolingual_v1",
                stream=True
            )
            
            for chunk in audio_stream:
                yield chunk
                
        except Exception as e:
            logging.error(f"Error in streaming synthesis: {str(e)}")
            raise e
    
    def get_available_voices(self):
        """Get list of available voices from Eleven Labs"""
        try:
            if not self.client:
                # Return default voices if API is not configured
                return [
                    {
                        'voice_id': '21m00Tcm4TlvDq8ikWAM',
                        'name': 'Rachel',
                        'category': 'premade',
                        'description': 'Young adult female voice'
                    },
                    {
                        'voice_id': 'AZnzlk1XvdvUeBnXmlld',
                        'name': 'Domi',
                        'category': 'premade',
                        'description': 'Young adult female voice'
                    }
                ]
            
            # Get voices from API
            voices_response = self.client.voices.get_all()
            
            voice_list = []
            for voice in voices_response.voices:
                voice_list.append({
                    'voice_id': voice.voice_id,
                    'name': voice.name,
                    'category': voice.category,
                    'description': voice.description or 'No description available'
                })
            
            return voice_list
            
        except Exception as e:
            logging.error(f"Error getting available voices: {str(e)}")
            return []
    
    def generate_sound_effect(self, description, duration=5.0):
        """Generate sound effects using Eleven Labs"""
        try:
            if not self.client:
                raise Exception("Voice service is not properly configured")
            
            # Use the sound effects API
            audio = self.client.text_to_sound_effects.convert(
                text=description,
                duration_seconds=duration
            )
            
            # Convert generator to bytes
            audio_bytes = b''.join(audio)
            return audio_bytes
                
        except Exception as e:
            logging.error(f"Error generating sound effect: {str(e)}")
            raise e
    
    def get_voice_settings(self):
        """Get current voice settings"""
        return self.voice_settings.copy()
    
    def update_voice_settings(self, new_settings):
        """Update voice settings"""
        try:
            # Validate and update settings
            if 'stability' in new_settings:
                self.voice_settings['stability'] = max(0.0, min(1.0, float(new_settings['stability'])))
            
            if 'similarity_boost' in new_settings:
                self.voice_settings['similarity_boost'] = max(0.0, min(1.0, float(new_settings['similarity_boost'])))
            
            if 'style' in new_settings:
                self.voice_settings['style'] = max(0.0, min(1.0, float(new_settings['style'])))
            
            if 'use_speaker_boost' in new_settings:
                self.voice_settings['use_speaker_boost'] = bool(new_settings['use_speaker_boost'])
            
            return self.voice_settings.copy()
            
        except Exception as e:
            logging.error(f"Error updating voice settings: {str(e)}")
            raise e
    
    def clone_voice(self, name, files):
        """Clone a voice from audio samples (placeholder for future implementation)"""
        try:
            # This would implement voice cloning functionality
            # For now, return a placeholder response
            return {
                'success': False,
                'message': 'Voice cloning feature coming soon'
            }
            
        except Exception as e:
            logging.error(f"Error cloning voice: {str(e)}")
            raise e
    
    def get_voice_info(self, voice_id):
        """Get information about a specific voice"""
        try:
            if not self.client:
                raise Exception("Voice service is not properly configured")

            voice = self.client.voices.get(voice_id=voice_id)

            return {
                'voice_id': voice.voice_id,
                'name': voice.name,
                'category': voice.category,
                'description': voice.description,
                'settings': voice.settings.__dict__ if voice.settings else None
            }

        except Exception as e:
            logging.error(f"Error getting voice info: {str(e)}")
            raise e

    def transcribe_audio(self, audio_data):
        """Convert speech to text using Google Speech Recognition"""
        try:
            # Create a temporary file to store the audio data
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name

            try:
                # Load audio file
                with sr.AudioFile(temp_file_path) as source:
                    # Adjust for ambient noise
                    self.recognizer.adjust_for_ambient_noise(source, duration=0.5)
                    # Record the audio
                    audio = self.recognizer.record(source)

                # Recognize speech using Google Speech Recognition
                text = self.recognizer.recognize_google(audio)

                return {
                    'text': text,
                    'confidence': 1.0,  # Google API doesn't return confidence
                    'success': True
                }

            finally:
                # Clean up temporary file
                os.unlink(temp_file_path)

        except sr.UnknownValueError:
            logging.warning("Could not understand audio")
            return {
                'text': '',
                'confidence': 0.0,
                'success': False,
                'error': 'Could not understand audio'
            }
        except sr.RequestError as e:
            logging.error(f"Error with speech recognition service: {str(e)}")
            return {
                'text': '',
                'confidence': 0.0,
                'success': False,
                'error': f'Speech recognition service error: {str(e)}'
            }
        except Exception as e:
            logging.error(f"Error transcribing audio: {str(e)}")
            return {
                'text': '',
                'confidence': 0.0,
                'success': False,
                'error': str(e)
            }

    def transcribe_audio_blob(self, audio_blob):
        """Convert audio blob to text"""
        try:
            # Convert blob to bytes if needed
            if hasattr(audio_blob, 'read'):
                audio_data = audio_blob.read()
            else:
                audio_data = audio_blob

            return self.transcribe_audio(audio_data)

        except Exception as e:
            logging.error(f"Error transcribing audio blob: {str(e)}")
            return {
                'text': '',
                'confidence': 0.0,
                'success': False,
                'error': str(e)
            }

