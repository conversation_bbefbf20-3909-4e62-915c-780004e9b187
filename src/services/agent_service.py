import os
import re
import json
from datetime import datetime
from src.services.ai_service import AIService
import logging

class AgentService:
    def __init__(self):
        self.ai_service = AIService()
        self.personality_prompt = ""
        self.training_mode = False
        self.personality_profiles = {}
        
        # Load saved personalities if they exist
        self._load_personalities()
        
        # Basic command patterns for personality training
        self.command_patterns = {
            r'set personality (.+)': 'set_personality',
            r'load personality (.+)': 'load_personality',
            r'save personality (.+)': 'save_personality',
            r'reset personality': 'reset_personality',
            r'training mode (on|off)': 'toggle_training',
            r'show personality': 'show_personality',
            r'list personalities': 'list_personalities',
            r'help.*with (.+)': 'general_help'
        }
        
        # Available commands for personality training
        self.available_commands = [
            {
                'name': 'set_personality',
                'description': 'Set the AI personality with a detailed prompt',
                'example': 'Set personality to be a wise mentor who speaks calmly',
                'parameters': ['personality_description']
            },
            {
                'name': 'load_personality',
                'description': 'Load a saved personality profile',
                'example': 'Load personality "wise_mentor"',
                'parameters': ['personality_name']
            },
            {
                'name': 'save_personality',
                'description': 'Save the current personality with a name',
                'example': 'Save personality as "wise_mentor"',
                'parameters': ['personality_name']
            },
            {
                'name': 'reset_personality',
                'description': 'Reset to default neutral personality',
                'example': 'Reset personality',
                'parameters': []
            },
            {
                'name': 'toggle_training',
                'description': 'Enable/disable training mode for personality refinement',
                'example': 'Training mode on',
                'parameters': ['mode']
            },
            {
                'name': 'show_personality',
                'description': 'Show the current personality settings',
                'example': 'Show personality',
                'parameters': []
            },
            {
                'name': 'list_personalities',
                'description': 'List all saved personality profiles',
                'example': 'List personalities',
                'parameters': []
            }
        ]
    
    def execute_command(self, command, context=None):
        """Execute a command based on intent analysis"""
        try:
            # Parse the command to understand intent
            parsed_command = self.parse_command(command)
            intent = parsed_command.get('intent')
            parameters = parsed_command.get('parameters', {})
            
            # Execute based on intent
            if intent == 'set_personality':
                return self._set_personality(parameters, command)
            elif intent == 'load_personality':
                return self._load_personality(parameters, command)
            elif intent == 'save_personality':
                return self._save_personality(parameters, command)
            elif intent == 'reset_personality':
                return self._reset_personality()
            elif intent == 'toggle_training':
                return self._toggle_training(parameters)
            elif intent == 'show_personality':
                return self._show_personality()
            elif intent == 'list_personalities':
                return self._list_personalities()
            elif intent == 'general_help':
                return self._general_help(parameters, command)
            else:
                return {
                    'success': False,
                    'message': 'I didn\'t understand that command. Try "help" to see available commands.',
                    'intent': intent
                }
                
        except Exception as e:
            logging.error(f"Error executing command: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Sorry, I encountered an error while processing your command.'
            }
    
    def parse_command(self, command):
        """Parse command to understand intent and extract parameters"""
        try:
            # First try pattern matching
            pattern_match = self._pattern_match_command(command)
            
            # Then try AI analysis for more complex commands
            ai_analysis = self.ai_service.analyze_command_intent(command)
            
            # Combine results, preferring AI analysis if confident
            if ai_analysis.get('confidence', 0) > 0.7:
                return ai_analysis
            elif pattern_match:
                return pattern_match
            else:
                return ai_analysis  # Return AI analysis even if low confidence
                
        except Exception as e:
            logging.error(f"Error parsing command: {str(e)}")
            return {
                'intent': 'unknown',
                'parameters': {},
                'confidence': 0.0,
                'error': str(e)
            }
    
    def _pattern_match_command(self, command):
        """Match command against known patterns"""
        command_lower = command.lower()
        
        for pattern, intent in self.command_patterns.items():
            match = re.search(pattern, command_lower)
            if match:
                parameters = {}
                if match.groups():
                    if intent == 'set_personality':
                        parameters['personality_description'] = match.group(1)
                    elif intent in ['load_personality', 'save_personality']:
                        parameters['personality_name'] = match.group(1)
                    elif intent == 'toggle_training':
                        parameters['mode'] = match.group(1)
                    else:
                        parameters['query'] = match.group(1)
                
                return {
                    'intent': intent,
                    'parameters': parameters,
                    'confidence': 0.9
                }
        
        return None
    
    def _set_personality(self, parameters, original_command):
        """Set the AI personality with a detailed prompt"""
        try:
            personality_description = parameters.get('personality_description') or parameters.get('query') or original_command.replace('set personality', '').strip()
            
            if not personality_description:
                return {
                    'success': False,
                    'message': 'Please provide a personality description. Example: "Set personality to be a wise mentor who speaks calmly"'
                }
            
            # Store the personality prompt
            self.personality_prompt = personality_description
            
            # Update the AI service with the new personality
            self.ai_service.set_personality(personality_description)
            
            return {
                'success': True,
                'personality': personality_description,
                'message': f'Personality set! I will now embody: {personality_description}'
            }
            
        except Exception as e:
            logging.error(f"Error setting personality: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble setting the personality. Please try again.'
            }
    
    def _load_personality(self, parameters, original_command):
        """Load a saved personality profile"""
        try:
            personality_name = parameters.get('personality_name') or parameters.get('query') or original_command.replace('load personality', '').strip()
            
            if not personality_name:
                return {
                    'success': False,
                    'message': 'Please specify a personality name to load. Use "list personalities" to see available options.'
                }
            
            if personality_name in self.personality_profiles:
                profile = self.personality_profiles[personality_name]
                self.personality_prompt = profile['prompt']
                self.ai_service.set_personality(profile['prompt'])
                
                return {
                    'success': True,
                    'personality': profile,
                    'message': f'Loaded personality "{personality_name}": {profile["prompt"]}'
                }
            else:
                return {
                    'success': False,
                    'message': f'Personality "{personality_name}" not found. Use "list personalities" to see available options.'
                }
                
        except Exception as e:
            logging.error(f"Error loading personality: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble loading the personality. Please try again.'
            }
    
    def _save_personality(self, parameters, original_command):
        """Save the current personality with a name"""
        try:
            personality_name = parameters.get('personality_name') or parameters.get('query') or original_command.replace('save personality', '').strip()
            
            if not personality_name:
                return {
                    'success': False,
                    'message': 'Please specify a name for the personality. Example: "Save personality as wise_mentor"'
                }
            
            if not self.personality_prompt:
                return {
                    'success': False,
                    'message': 'No personality is currently set. Set a personality first before saving.'
                }
            
            self.personality_profiles[personality_name] = {
                'prompt': self.personality_prompt,
                'created_at': datetime.now().isoformat(),
                'training_mode': self.training_mode
            }
            
            self._save_personalities()
            
            return {
                'success': True,
                'message': f'Personality saved as "{personality_name}": {self.personality_prompt}'
            }
            
        except Exception as e:
            logging.error(f"Error saving personality: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble saving the personality. Please try again.'
            }
    
    def _reset_personality(self):
        """Reset to default neutral personality"""
        try:
            self.personality_prompt = ""
            self.ai_service.reset_personality()
            
            return {
                'success': True,
                'message': 'Personality reset to default neutral mode.'
            }
            
        except Exception as e:
            logging.error(f"Error resetting personality: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble resetting the personality. Please try again.'
            }
    
    def _toggle_training(self, parameters):
        """Toggle training mode on/off"""
        try:
            mode = parameters.get('mode', '').lower()
            
            if mode == 'on':
                self.training_mode = True
                message = 'Training mode enabled. I will be more receptive to personality adjustments.'
            elif mode == 'off':
                self.training_mode = False
                message = 'Training mode disabled. I will maintain my current personality.'
            else:
                self.training_mode = not self.training_mode
                message = f'Training mode {"enabled" if self.training_mode else "disabled"}.'
            
            return {
                'success': True,
                'training_mode': self.training_mode,
                'message': message
            }
            
        except Exception as e:
            logging.error(f"Error toggling training mode: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble changing training mode. Please try again.'
            }
    
    def _show_personality(self):
        """Show current personality settings"""
        try:
            if self.personality_prompt:
                return {
                    'success': True,
                    'personality': self.personality_prompt,
                    'training_mode': self.training_mode,
                    'message': f'Current personality: {self.personality_prompt}\nTraining mode: {"On" if self.training_mode else "Off"}'
                }
            else:
                return {
                    'success': True,
                    'message': 'No personality is currently set. I\'m in default neutral mode.'
                }
                
        except Exception as e:
            logging.error(f"Error showing personality: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble showing the personality. Please try again.'
            }
    
    def _list_personalities(self):
        """List all saved personality profiles"""
        try:
            if not self.personality_profiles:
                return {
                    'success': True,
                    'message': 'No saved personalities found. Create one by setting a personality and then saving it.'
                }
            
            personality_list = []
            for name, profile in self.personality_profiles.items():
                personality_list.append(f"• {name}: {profile['prompt'][:100]}{'...' if len(profile['prompt']) > 100 else ''}")
            
            return {
                'success': True,
                'personalities': self.personality_profiles,
                'message': f'Saved personalities:\n' + '\n'.join(personality_list)
            }
            
        except Exception as e:
            logging.error(f"Error listing personalities: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble listing personalities. Please try again.'
            }
    
    def _general_help(self, parameters, original_command):
        """Provide help information"""
        try:
            help_text = """
Personality Training Commands:
• "Set personality [description]" - Define a new personality
• "Save personality [name]" - Save current personality
• "Load personality [name]" - Load a saved personality
• "List personalities" - Show all saved personalities
• "Show personality" - Display current personality
• "Reset personality" - Return to neutral mode
• "Training mode on/off" - Toggle training mode

Example: "Set personality to be a wise mentor who speaks calmly and gives thoughtful advice"
            """
            
            return {
                'success': True,
                'message': help_text.strip()
            }
            
        except Exception as e:
            logging.error(f"Error providing help: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble providing help. Please try again.'
            }
    
    def _load_personalities(self):
        """Load saved personalities from file"""
        try:
            personalities_file = 'personalities.json'
            if os.path.exists(personalities_file):
                with open(personalities_file, 'r') as f:
                    self.personality_profiles = json.load(f)
        except Exception as e:
            logging.error(f"Error loading personalities: {str(e)}")
            self.personality_profiles = {}
    
    def _save_personalities(self):
        """Save personalities to file"""
        try:
            personalities_file = 'personalities.json'
            with open(personalities_file, 'w') as f:
                json.dump(self.personality_profiles, f, indent=2)
        except Exception as e:
            logging.error(f"Error saving personalities: {str(e)}")
    
    def get_available_commands(self):
        """Get list of available commands"""
        return self.available_commands
