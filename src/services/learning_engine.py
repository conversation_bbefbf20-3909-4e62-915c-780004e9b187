import os
import json
import logging
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional, Tuple
from openai import OpenAI
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
from src.models.user import db
from src.models.personality_memory import (
    PersonalityProfile, PersonalityMemory, TrainingExample, 
    BehavioralPattern, PersonalityEvolution
)

class LearningEngine:
    def __init__(self):
        self.openai_client = None
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        if self.openai_api_key:
            self.openai_client = OpenAI(api_key=self.openai_api_key)
        else:
            logging.warning("OpenAI API key not found. Advanced learning features will be limited.")
        
        # Learning parameters
        self.embedding_model = "text-embedding-3-small"  # Cost-effective embedding model
        self.similarity_threshold = 0.75  # Minimum similarity for pattern recognition
        self.pattern_emergence_threshold = 3  # Minimum examples needed for pattern
        self.evolution_confidence_threshold = 0.8
        
    def analyze_training_example(self, example: <PERSON><PERSON>xample) -> Dict:
        """Analyze a training example and extract learning insights"""
        try:
            if not self.openai_client:
                return self._basic_training_analysis(example)
            
            analysis_prompt = f"""
            Analyze this personality training example and extract key learning insights:
            
            Scenario: {example.scenario}
            Expected Response: {example.expected_response}
            Explanation: {example.explanation or 'None provided'}
            Category: {example.category or 'General'}
            
            Extract and return JSON with:
            1. "speech_patterns": List of specific speech patterns or phrases to learn
            2. "behavioral_traits": Key personality traits demonstrated
            3. "response_triggers": What types of inputs should trigger this response style
            4. "emotional_tone": The emotional tone/mood of the response
            5. "key_concepts": Important concepts or themes
            6. "learning_priority": How important this example is (1-10)
            7. "pattern_category": What type of behavioral pattern this represents
            
            Be specific and actionable in your analysis.
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.3
            )
            
            analysis = json.loads(response.choices[0].message.content)
            
            # Store embedding for semantic similarity
            if self.openai_client:
                embedding = self._get_embedding(f"{example.scenario} {example.expected_response}")
                analysis['embedding'] = embedding.tolist() if embedding is not None else None
            
            return analysis
            
        except Exception as e:
            logging.error(f"Error analyzing training example: {str(e)}")
            return self._basic_training_analysis(example)
    
    def find_similar_memories(self, query_text: str, personality_id: int, 
                            limit: int = 10, min_similarity: float = None) -> List[Tuple[PersonalityMemory, float]]:
        """Find memories similar to the query using semantic similarity"""
        try:
            if not self.openai_client:
                return self._basic_memory_search(query_text, personality_id, limit)
            
            min_similarity = min_similarity or self.similarity_threshold
            
            # Get embedding for query
            query_embedding = self._get_embedding(query_text)
            if query_embedding is None:
                return []
            
            # Get all memories for this personality
            memories = PersonalityMemory.query.filter_by(personality_id=personality_id).all()
            
            similar_memories = []
            for memory in memories:
                # Get or create embedding for memory
                memory_embedding = self._get_memory_embedding(memory)
                if memory_embedding is not None:
                    similarity = cosine_similarity([query_embedding], [memory_embedding])[0][0]
                    if similarity >= min_similarity:
                        similar_memories.append((memory, similarity))
            
            # Sort by similarity and return top results
            similar_memories.sort(key=lambda x: x[1], reverse=True)
            return similar_memories[:limit]
            
        except Exception as e:
            logging.error(f"Error finding similar memories: {str(e)}")
            return []
    
    def detect_behavioral_patterns(self, personality_id: int) -> List[BehavioralPattern]:
        """Detect emerging behavioral patterns from training examples and memories"""
        try:
            # Get training examples and memories
            examples = TrainingExample.query.filter_by(personality_id=personality_id).all()
            memories = PersonalityMemory.query.filter_by(personality_id=personality_id).all()
            
            if len(examples) < self.pattern_emergence_threshold:
                logging.info(f"Not enough training examples ({len(examples)}) to detect patterns")
                return []
            
            # Analyze patterns using AI
            patterns = []
            
            # Group examples by category
            categories = {}
            for example in examples:
                category = example.category or 'general'
                if category not in categories:
                    categories[category] = []
                categories[category].append(example)
            
            # Analyze each category for patterns
            for category, category_examples in categories.items():
                if len(category_examples) >= self.pattern_emergence_threshold:
                    category_patterns = self._analyze_category_patterns(category, category_examples, personality_id)
                    patterns.extend(category_patterns)
            
            # Cross-category pattern analysis
            cross_patterns = self._analyze_cross_category_patterns(examples, personality_id)
            patterns.extend(cross_patterns)
            
            return patterns
            
        except Exception as e:
            logging.error(f"Error detecting behavioral patterns: {str(e)}")
            return []
    
    def suggest_personality_evolution(self, personality_id: int) -> List[Dict]:
        """Suggest how the personality could evolve based on learning"""
        try:
            if not self.openai_client:
                return []
            
            # Get recent memories and patterns
            recent_memories = PersonalityMemory.query.filter(
                PersonalityMemory.personality_id == personality_id,
                PersonalityMemory.created_at >= datetime.utcnow() - timedelta(days=7)
            ).order_by(PersonalityMemory.created_at.desc()).limit(20).all()
            
            patterns = BehavioralPattern.query.filter_by(personality_id=personality_id).all()
            
            if not recent_memories and not patterns:
                return []
            
            # Analyze for evolution opportunities
            analysis_prompt = f"""
            Analyze this personality's recent development and suggest evolution opportunities:
            
            Recent Interactions ({len(recent_memories)} memories):
            {self._format_memories_for_analysis(recent_memories[:10])}
            
            Learned Patterns ({len(patterns)} patterns):
            {self._format_patterns_for_analysis(patterns[:5])}
            
            Suggest 3-5 personality evolution opportunities in JSON format:
            [
                {{
                    "evolution_type": "trait_development|speech_pattern|emotional_growth|behavioral_refinement",
                    "description": "What would evolve",
                    "trigger_conditions": "What would trigger this evolution",
                    "expected_outcome": "How the personality would change",
                    "confidence": 0.0-1.0,
                    "priority": 1-10
                }}
            ]
            
            Focus on natural, gradual developments that build on existing patterns.
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.4
            )
            
            suggestions = json.loads(response.choices[0].message.content)
            return suggestions
            
        except Exception as e:
            logging.error(f"Error suggesting personality evolution: {str(e)}")
            return []
    
    def learn_from_feedback(self, memory: PersonalityMemory, feedback_type: str, 
                          feedback_text: str = None, score: float = 0.0) -> Dict:
        """Learn from user feedback and update patterns"""
        try:
            learning_insights = {
                'pattern_updates': [],
                'new_patterns': [],
                'evolution_triggers': []
            }
            
            if feedback_type == 'correction' and feedback_text:
                # Analyze what went wrong and how to improve
                correction_analysis = self._analyze_correction(memory, feedback_text)
                learning_insights['pattern_updates'].extend(correction_analysis.get('pattern_updates', []))
                learning_insights['new_patterns'].extend(correction_analysis.get('new_patterns', []))
            
            elif feedback_type in ['positive', 'negative']:
                # Reinforce or discourage patterns
                reinforcement_analysis = self._analyze_reinforcement(memory, feedback_type, score)
                learning_insights['pattern_updates'].extend(reinforcement_analysis.get('pattern_updates', []))
            
            # Apply learning insights
            self._apply_learning_insights(memory.personality_id, learning_insights)
            
            return learning_insights
            
        except Exception as e:
            logging.error(f"Error learning from feedback: {str(e)}")
            return {}
    
    def _get_embedding(self, text: str) -> Optional[np.ndarray]:
        """Get OpenAI embedding for text"""
        try:
            if not self.openai_client:
                return None
            
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=text
            )
            
            return np.array(response.data[0].embedding)
            
        except Exception as e:
            logging.error(f"Error getting embedding: {str(e)}")
            return None
    
    def _get_memory_embedding(self, memory: PersonalityMemory) -> Optional[np.ndarray]:
        """Get or create embedding for a memory"""
        try:
            # Check if embedding is already stored in context
            if memory.context and 'embedding' in memory.context:
                return np.array(memory.context['embedding'])
            
            # Create new embedding
            text = f"{memory.user_input} {memory.ai_response}"
            embedding = self._get_embedding(text)
            
            if embedding is not None:
                # Store embedding in context for future use
                if not memory.context:
                    memory.context = {}
                memory.context['embedding'] = embedding.tolist()
                db.session.commit()
            
            return embedding
            
        except Exception as e:
            logging.error(f"Error getting memory embedding: {str(e)}")
            return None
    
    def _basic_training_analysis(self, example: TrainingExample) -> Dict:
        """Basic analysis when OpenAI is not available"""
        return {
            'speech_patterns': [],
            'behavioral_traits': [example.category or 'general'],
            'response_triggers': ['general'],
            'emotional_tone': 'neutral',
            'key_concepts': [],
            'learning_priority': example.priority,
            'pattern_category': example.category or 'general'
        }
    
    def _basic_memory_search(self, query_text: str, personality_id: int, limit: int) -> List[Tuple[PersonalityMemory, float]]:
        """Basic keyword-based memory search when OpenAI is not available"""
        try:
            keywords = query_text.lower().split()
            memories = PersonalityMemory.query.filter_by(personality_id=personality_id).all()
            
            scored_memories = []
            for memory in memories:
                score = 0
                text = f"{memory.user_input} {memory.ai_response}".lower()
                for keyword in keywords:
                    if keyword in text:
                        score += 1
                
                if score > 0:
                    scored_memories.append((memory, score / len(keywords)))
            
            scored_memories.sort(key=lambda x: x[1], reverse=True)
            return scored_memories[:limit]
            
        except Exception as e:
            logging.error(f"Error in basic memory search: {str(e)}")
            return []
    
    def _analyze_category_patterns(self, category: str, examples: List[TrainingExample], 
                                 personality_id: int) -> List[BehavioralPattern]:
        """Analyze patterns within a specific category"""
        try:
            if not self.openai_client or len(examples) < self.pattern_emergence_threshold:
                return []
            
            # Format examples for analysis
            examples_text = "\n".join([
                f"Scenario: {ex.scenario}\nResponse: {ex.expected_response}\n"
                for ex in examples[:10]  # Limit to avoid token limits
            ])
            
            analysis_prompt = f"""
            Analyze these training examples from category "{category}" and identify behavioral patterns:
            
            {examples_text}
            
            Identify consistent patterns in JSON format:
            [
                {{
                    "pattern_name": "Brief descriptive name",
                    "pattern_description": "Detailed description of the pattern",
                    "trigger_conditions": ["condition1", "condition2"],
                    "response_template": "Template for responses following this pattern",
                    "confidence_score": 0.0-1.0,
                    "examples_count": {len(examples)}
                }}
            ]
            
            Only include patterns that appear in at least 3 examples.
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.3
            )
            
            patterns_data = json.loads(response.choices[0].message.content)
            
            # Create BehavioralPattern objects
            patterns = []
            for pattern_data in patterns_data:
                if pattern_data.get('confidence_score', 0) >= self.similarity_threshold:
                    pattern = BehavioralPattern(
                        personality_id=personality_id,
                        pattern_name=pattern_data['pattern_name'],
                        pattern_description=pattern_data['pattern_description'],
                        trigger_conditions=pattern_data.get('trigger_conditions', []),
                        response_template=pattern_data.get('response_template', ''),
                        confidence_score=pattern_data.get('confidence_score', 0.0),
                        usage_count=0,
                        success_rate=0.0,
                        learned_from_examples=[ex.id for ex in examples]
                    )
                    patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logging.error(f"Error analyzing category patterns: {str(e)}")
            return []
    
    def _analyze_cross_category_patterns(self, examples: List[TrainingExample], 
                                       personality_id: int) -> List[BehavioralPattern]:
        """Analyze patterns that span across categories"""
        try:
            if not self.openai_client or len(examples) < self.pattern_emergence_threshold * 2:
                return []
            
            # This would analyze overarching personality traits that appear across different categories
            # For now, return empty list - can be enhanced later
            return []
            
        except Exception as e:
            logging.error(f"Error analyzing cross-category patterns: {str(e)}")
            return []
    
    def _analyze_correction(self, memory: PersonalityMemory, feedback_text: str) -> Dict:
        """Analyze correction feedback to extract learning insights"""
        try:
            if not self.openai_client:
                return {'pattern_updates': [], 'new_patterns': []}
            
            analysis_prompt = f"""
            Analyze this correction feedback to extract learning insights:
            
            Original User Input: {memory.user_input}
            AI Response (incorrect): {memory.ai_response}
            Correction Feedback: {feedback_text}
            
            Extract learning insights in JSON format:
            {{
                "what_went_wrong": "Analysis of the mistake",
                "correct_approach": "How it should have been handled",
                "pattern_updates": [
                    {{
                        "pattern_type": "speech_pattern|behavior|emotional_response",
                        "update_description": "What to change",
                        "priority": 1-10
                    }}
                ],
                "new_patterns": [
                    {{
                        "pattern_name": "Name for new pattern to learn",
                        "description": "What this pattern should do",
                        "trigger": "When to use this pattern"
                    }}
                ]
            }}
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.3
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            logging.error(f"Error analyzing correction: {str(e)}")
            return {'pattern_updates': [], 'new_patterns': []}
    
    def _analyze_reinforcement(self, memory: PersonalityMemory, feedback_type: str, score: float) -> Dict:
        """Analyze positive/negative reinforcement"""
        # Simple reinforcement analysis - can be enhanced
        return {
            'pattern_updates': [{
                'pattern_type': 'reinforcement',
                'update_description': f'{feedback_type} reinforcement with score {score}',
                'priority': abs(score)
            }]
        }
    
    def _apply_learning_insights(self, personality_id: int, insights: Dict):
        """Apply learning insights to update patterns and personality"""
        try:
            # Update existing patterns
            for update in insights.get('pattern_updates', []):
                # Find and update relevant patterns
                # This would be implemented based on specific update types
                pass
            
            # Create new patterns
            for new_pattern in insights.get('new_patterns', []):
                pattern = BehavioralPattern(
                    personality_id=personality_id,
                    pattern_name=new_pattern.get('pattern_name', 'Learned Pattern'),
                    pattern_description=new_pattern.get('description', ''),
                    trigger_conditions=[new_pattern.get('trigger', '')],
                    confidence_score=0.5,  # Start with medium confidence
                    usage_count=0,
                    success_rate=0.0
                )
                db.session.add(pattern)
            
            db.session.commit()
            
        except Exception as e:
            logging.error(f"Error applying learning insights: {str(e)}")
            db.session.rollback()
    
    def _format_memories_for_analysis(self, memories: List[PersonalityMemory]) -> str:
        """Format memories for AI analysis"""
        formatted = []
        for memory in memories:
            formatted.append(f"User: {memory.user_input}\nAI: {memory.ai_response}\nImportance: {memory.importance_score}")
        return "\n---\n".join(formatted)
    
    def _format_patterns_for_analysis(self, patterns: List[BehavioralPattern]) -> str:
        """Format patterns for AI analysis"""
        formatted = []
        for pattern in patterns:
            formatted.append(f"Pattern: {pattern.pattern_name}\nDescription: {pattern.pattern_description}\nConfidence: {pattern.confidence_score}")
        return "\n---\n".join(formatted)
