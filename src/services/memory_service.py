import os
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from sqlalchemy import and_, or_, desc, func
from src.models.user import db
from src.models.personality_memory import (
    PersonalityProfile, PersonalityMemory, TrainingExample, 
    BehavioralPattern, PersonalityEvolution, MemoryTag
)
from src.services.ai_service import AIService

class MemoryService:
    def __init__(self):
        self.ai_service = AIService()
        self.current_personality_id = None
        
        # Memory configuration
        self.max_context_memories = 10  # Max memories to include in context
        self.importance_threshold = 3.0  # Minimum importance for long-term storage
        self.pattern_confidence_threshold = 0.7  # Minimum confidence for behavioral patterns
        
    def set_active_personality(self, personality_id: int):
        """Set the currently active personality for memory operations"""
        self.current_personality_id = personality_id
        
    def store_interaction_memory(self, user_input: str, ai_response: str, 
                                conversation_id: int = None, context: Dict = None,
                                memory_type: str = 'conversation') -> PersonalityMemory:
        """Store a new interaction memory"""
        try:
            if not self.current_personality_id:
                logging.warning("No active personality set for memory storage")
                return None
            
            # Analyze importance and emotional context
            importance_score = self._analyze_importance(user_input, ai_response, context)
            emotional_context = self._analyze_emotional_context(user_input, ai_response)
            tags = self._generate_tags(user_input, ai_response)
            
            # Create memory record
            memory = PersonalityMemory(
                personality_id=self.current_personality_id,
                conversation_id=conversation_id,
                user_input=user_input,
                ai_response=ai_response,
                context=context,
                memory_type=memory_type,
                importance_score=importance_score,
                emotional_context=emotional_context,
                tags=tags
            )
            
            db.session.add(memory)
            db.session.commit()
            
            # Update personality last_updated
            self._update_personality_timestamp()
            
            # Trigger learning analysis if this is an important memory
            if importance_score >= self.importance_threshold:
                self._analyze_for_patterns(memory)
            
            logging.info(f"Stored memory {memory.id} with importance {importance_score}")
            return memory
            
        except Exception as e:
            logging.error(f"Error storing interaction memory: {str(e)}")
            db.session.rollback()
            return None
    
    def add_training_example(self, scenario: str, expected_response: str, 
                           explanation: str = None, category: str = None,
                           priority: int = 5, tags: List[str] = None) -> TrainingExample:
        """Add a new training example"""
        try:
            if not self.current_personality_id:
                logging.warning("No active personality set for training example")
                return None
            
            example = TrainingExample(
                personality_id=self.current_personality_id,
                scenario=scenario,
                expected_response=expected_response,
                explanation=explanation,
                category=category,
                priority=priority,
                tags=tags
            )
            
            db.session.add(example)
            db.session.commit()
            
            # Analyze this training example for immediate pattern learning
            self._learn_from_training_example(example)
            
            logging.info(f"Added training example {example.id}")
            return example
            
        except Exception as e:
            logging.error(f"Error adding training example: {str(e)}")
            db.session.rollback()
            return None
    
    def get_relevant_memories(self, current_input: str, limit: int = None) -> List[PersonalityMemory]:
        """Retrieve memories relevant to the current input"""
        try:
            if not self.current_personality_id:
                return []
            
            limit = limit or self.max_context_memories
            
            # Get memories using multiple strategies
            memories = []
            
            # 1. Exact keyword matches
            keyword_memories = self._get_keyword_matching_memories(current_input, limit // 3)
            memories.extend(keyword_memories)
            
            # 2. Tag-based matches
            tag_memories = self._get_tag_matching_memories(current_input, limit // 3)
            memories.extend(tag_memories)
            
            # 3. High importance recent memories
            recent_memories = self._get_recent_important_memories(limit // 3)
            memories.extend(recent_memories)
            
            # Remove duplicates and sort by relevance
            unique_memories = list({m.id: m for m in memories}.values())
            sorted_memories = sorted(unique_memories, 
                                   key=lambda m: (m.importance_score, m.created_at), 
                                   reverse=True)
            
            return sorted_memories[:limit]
            
        except Exception as e:
            logging.error(f"Error retrieving relevant memories: {str(e)}")
            return []
    
    def get_training_examples(self, category: str = None, limit: int = 20) -> List[TrainingExample]:
        """Get training examples for the current personality"""
        try:
            if not self.current_personality_id:
                return []
            
            query = TrainingExample.query.filter_by(personality_id=self.current_personality_id)
            
            if category:
                query = query.filter_by(category=category)
            
            examples = query.order_by(desc(TrainingExample.priority), 
                                    desc(TrainingExample.effectiveness_score)).limit(limit).all()
            
            return examples
            
        except Exception as e:
            logging.error(f"Error retrieving training examples: {str(e)}")
            return []
    
    def get_behavioral_patterns(self, min_confidence: float = None) -> List[BehavioralPattern]:
        """Get learned behavioral patterns"""
        try:
            if not self.current_personality_id:
                return []
            
            min_confidence = min_confidence or self.pattern_confidence_threshold
            
            patterns = BehavioralPattern.query.filter(
                and_(
                    BehavioralPattern.personality_id == self.current_personality_id,
                    BehavioralPattern.confidence_score >= min_confidence
                )
            ).order_by(desc(BehavioralPattern.confidence_score)).all()
            
            return patterns
            
        except Exception as e:
            logging.error(f"Error retrieving behavioral patterns: {str(e)}")
            return []
    
    def provide_feedback(self, memory_id: int, feedback_type: str, 
                        feedback_text: str = None, score: float = 0.0):
        """Provide feedback on an AI response for learning"""
        try:
            memory = PersonalityMemory.query.get(memory_id)
            if not memory:
                logging.warning(f"Memory {memory_id} not found for feedback")
                return False
            
            if feedback_type == 'correction':
                memory.was_corrected = True
                memory.correction_feedback = feedback_text
                memory.reinforcement_score = -abs(score)  # Negative for corrections
            elif feedback_type == 'positive':
                memory.reinforcement_score = abs(score)  # Positive reinforcement
            elif feedback_type == 'negative':
                memory.reinforcement_score = -abs(score)  # Negative reinforcement
            
            db.session.commit()
            
            # Learn from this feedback
            self._learn_from_feedback(memory, feedback_type, feedback_text, score)
            
            logging.info(f"Applied {feedback_type} feedback to memory {memory_id}")
            return True
            
        except Exception as e:
            logging.error(f"Error providing feedback: {str(e)}")
            db.session.rollback()
            return False
    
    def evolve_personality(self, evolution_type: str, description: str, 
                          trigger_event: str = None, before_state: Dict = None,
                          after_state: Dict = None) -> PersonalityEvolution:
        """Record personality evolution/development"""
        try:
            if not self.current_personality_id:
                return None
            
            evolution = PersonalityEvolution(
                personality_id=self.current_personality_id,
                evolution_type=evolution_type,
                description=description,
                trigger_event=trigger_event,
                before_state=before_state,
                after_state=after_state,
                confidence=0.8,  # Default confidence
                impact_score=5.0  # Default impact
            )
            
            db.session.add(evolution)
            db.session.commit()
            
            # Update personality current_state
            self._update_personality_state(after_state)
            
            logging.info(f"Recorded personality evolution: {evolution_type}")
            return evolution
            
        except Exception as e:
            logging.error(f"Error recording personality evolution: {str(e)}")
            db.session.rollback()
            return None
    
    def _analyze_importance(self, user_input: str, ai_response: str, context: Dict = None) -> float:
        """Analyze the importance of an interaction (0-10 scale)"""
        try:
            # Use AI to analyze importance
            analysis_prompt = f"""
            Analyze the importance of this interaction for personality learning (0-10 scale):
            
            User: {user_input}
            AI: {ai_response}
            Context: {json.dumps(context) if context else 'None'}
            
            Consider:
            - Emotional significance
            - Personality-revealing content
            - Training value
            - Uniqueness of interaction
            
            Return only a number between 0-10.
            """
            
            response = self.ai_service.model.generate_content(analysis_prompt)
            
            try:
                score = float(response.text.strip())
                return max(0.0, min(10.0, score))  # Clamp to 0-10
            except:
                return 5.0  # Default importance
                
        except Exception as e:
            logging.error(f"Error analyzing importance: {str(e)}")
            return 5.0  # Default importance
    
    def _analyze_emotional_context(self, user_input: str, ai_response: str) -> Dict:
        """Analyze emotional context of interaction"""
        try:
            analysis_prompt = f"""
            Analyze the emotional context of this interaction. Return JSON format:
            
            User: {user_input}
            AI: {ai_response}
            
            Return JSON with:
            - user_emotion: primary emotion of user
            - ai_emotion: emotional tone of AI response
            - interaction_mood: overall mood of interaction
            - emotional_intensity: 1-10 scale
            """
            
            response = self.ai_service.model.generate_content(analysis_prompt)
            
            try:
                return json.loads(response.text.strip())
            except:
                return {
                    'user_emotion': 'neutral',
                    'ai_emotion': 'neutral', 
                    'interaction_mood': 'neutral',
                    'emotional_intensity': 5
                }
                
        except Exception as e:
            logging.error(f"Error analyzing emotional context: {str(e)}")
            return {}
    
    def _generate_tags(self, user_input: str, ai_response: str) -> List[str]:
        """Generate searchable tags for the interaction"""
        try:
            # Simple keyword extraction for now
            # Could be enhanced with NLP libraries
            text = f"{user_input} {ai_response}".lower()
            
            # Basic tag categories
            tags = []
            
            # Emotion tags
            emotions = ['happy', 'sad', 'angry', 'excited', 'calm', 'worried', 'confused']
            for emotion in emotions:
                if emotion in text:
                    tags.append(f"emotion:{emotion}")
            
            # Topic tags
            topics = ['personality', 'training', 'memory', 'behavior', 'response', 'learning']
            for topic in topics:
                if topic in text:
                    tags.append(f"topic:{topic}")
            
            # Question/statement tags
            if '?' in user_input:
                tags.append('type:question')
            else:
                tags.append('type:statement')
            
            return tags
            
        except Exception as e:
            logging.error(f"Error generating tags: {str(e)}")
            return []
    
    def _get_keyword_matching_memories(self, input_text: str, limit: int) -> List[PersonalityMemory]:
        """Get memories that match keywords in the input"""
        try:
            # Simple keyword matching - could be enhanced with better NLP
            keywords = input_text.lower().split()
            
            memories = PersonalityMemory.query.filter(
                and_(
                    PersonalityMemory.personality_id == self.current_personality_id,
                    or_(*[PersonalityMemory.user_input.ilike(f'%{keyword}%') for keyword in keywords])
                )
            ).order_by(desc(PersonalityMemory.importance_score)).limit(limit).all()
            
            return memories
            
        except Exception as e:
            logging.error(f"Error getting keyword matching memories: {str(e)}")
            return []
    
    def _get_tag_matching_memories(self, input_text: str, limit: int) -> List[PersonalityMemory]:
        """Get memories with matching tags"""
        try:
            # Generate tags for current input
            current_tags = self._generate_tags(input_text, "")
            
            if not current_tags:
                return []
            
            # Find memories with overlapping tags
            memories = PersonalityMemory.query.filter(
                PersonalityMemory.personality_id == self.current_personality_id
            ).all()
            
            matching_memories = []
            for memory in memories:
                if memory.tags:
                    overlap = set(current_tags) & set(memory.tags)
                    if overlap:
                        matching_memories.append(memory)
            
            # Sort by importance and return top matches
            matching_memories.sort(key=lambda m: m.importance_score, reverse=True)
            return matching_memories[:limit]
            
        except Exception as e:
            logging.error(f"Error getting tag matching memories: {str(e)}")
            return []
    
    def _get_recent_important_memories(self, limit: int) -> List[PersonalityMemory]:
        """Get recent high-importance memories"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=7)  # Last week
            
            memories = PersonalityMemory.query.filter(
                and_(
                    PersonalityMemory.personality_id == self.current_personality_id,
                    PersonalityMemory.importance_score >= self.importance_threshold,
                    PersonalityMemory.created_at >= cutoff_date
                )
            ).order_by(desc(PersonalityMemory.importance_score)).limit(limit).all()
            
            return memories
            
        except Exception as e:
            logging.error(f"Error getting recent important memories: {str(e)}")
            return []
    
    def _update_personality_timestamp(self):
        """Update the personality's last_updated timestamp"""
        try:
            if self.current_personality_id:
                personality = PersonalityProfile.query.get(self.current_personality_id)
                if personality:
                    personality.last_updated = datetime.utcnow()
                    db.session.commit()
        except Exception as e:
            logging.error(f"Error updating personality timestamp: {str(e)}")
    
    def _update_personality_state(self, new_state: Dict):
        """Update the personality's current state"""
        try:
            if self.current_personality_id and new_state:
                personality = PersonalityProfile.query.get(self.current_personality_id)
                if personality:
                    current_state = personality.current_state or {}
                    current_state.update(new_state)
                    personality.current_state = current_state
                    db.session.commit()
        except Exception as e:
            logging.error(f"Error updating personality state: {str(e)}")
    
    def _analyze_for_patterns(self, memory: PersonalityMemory):
        """Analyze a memory for behavioral patterns"""
        # This will be implemented in the learning algorithm
        pass
    
    def _learn_from_training_example(self, example: TrainingExample):
        """Learn from a new training example"""
        # This will be implemented in the learning algorithm
        pass
    
    def _learn_from_feedback(self, memory: PersonalityMemory, feedback_type: str, 
                           feedback_text: str, score: float):
        """Learn from user feedback"""
        # This will be implemented in the learning algorithm
        pass
