import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { BookOpen, Plus, Target, TrendingUp, Brain, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface TrainingExample {
  id: number
  scenario: string
  expected_response: string
  explanation?: string
  category?: string
  priority: number
  effectiveness_score: number
  created_at: string
}

interface BehavioralPattern {
  id: number
  pattern_name: string
  pattern_description: string
  confidence_score: number
  usage_count: number
  success_rate: number
}

interface TrainingInterfaceProps {
  socket: any
  isVisible: boolean
  onClose: () => void
  currentPersonality: string
}

const TrainingInterface: React.FC<TrainingInterfaceProps> = ({
  socket,
  isVisible,
  onClose,
  currentPersonality
}) => {
  const [trainingExamples, setTrainingExamples] = useState<TrainingExample[]>([])
  const [behavioralPatterns, setBehavioralPatterns] = useState<BehavioralPattern[]>([])
  const [isTrainingMode, setIsTrainingMode] = useState(false)
  
  // New training example form
  const [newExample, setNewExample] = useState({
    scenario: '',
    expected_response: '',
    explanation: '',
    category: '',
    priority: 5
  })
  
  // Feedback form
  const [feedbackMode, setFeedbackMode] = useState(false)
  const [selectedMessageId, setSelectedMessageId] = useState<number | null>(null)
  const [feedbackText, setFeedbackText] = useState('')
  const [feedbackType, setFeedbackType] = useState<'positive' | 'negative' | 'correction'>('correction')

  useEffect(() => {
    if (socket && isVisible) {
      // Request training data
      socket.emit('get_training_data', {
        personality_id: currentPersonality
      })
      
      // Check training mode status
      socket.emit('execute_command', {
        command: 'show personality',
        context: {}
      })
    }
  }, [socket, isVisible, currentPersonality])

  useEffect(() => {
    if (socket) {
      socket.on('training_data_received', (data: any) => {
        setTrainingExamples(data.training_examples || [])
        setBehavioralPatterns(data.behavioral_patterns || [])
      })

      socket.on('training_example_added', (data: any) => {
        if (data.success) {
          setTrainingExamples(prev => [...prev, data.example])
          setNewExample({
            scenario: '',
            expected_response: '',
            explanation: '',
            category: '',
            priority: 5
          })
        }
      })

      socket.on('feedback_applied', (data: any) => {
        if (data.success) {
          setFeedbackMode(false)
          setFeedbackText('')
          setSelectedMessageId(null)
        }
      })

      return () => {
        socket.off('training_data_received')
        socket.off('training_example_added')
        socket.off('feedback_applied')
      }
    }
  }, [socket])

  const handleAddTrainingExample = () => {
    if (!newExample.scenario.trim() || !newExample.expected_response.trim()) {
      return
    }

    socket?.emit('add_training_example', {
      scenario: newExample.scenario,
      expected_response: newExample.expected_response,
      explanation: newExample.explanation,
      category: newExample.category,
      priority: newExample.priority
    })
  }

  const handleProvideFeedback = () => {
    if (!selectedMessageId || !feedbackText.trim()) {
      return
    }

    socket?.emit('provide_feedback', {
      message_id: selectedMessageId,
      feedback_type: feedbackType,
      feedback_text: feedbackText,
      score: feedbackType === 'positive' ? 8.0 : feedbackType === 'negative' ? -6.0 : -8.0
    })
  }

  const toggleTrainingMode = () => {
    const newMode = !isTrainingMode
    setIsTrainingMode(newMode)
    
    socket?.emit('execute_command', {
      command: `training mode ${newMode ? 'on' : 'off'}`,
      context: {}
    })
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="glass-panel rounded-3xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <BookOpen className="w-6 h-6 text-purple-400" />
              <h2 className="text-2xl font-bold text-white/90">Training Interface</h2>
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                isTrainingMode 
                  ? 'bg-green-500/20 text-green-300 border border-green-500/30' 
                  : 'bg-gray-500/20 text-gray-300 border border-gray-500/30'
              }`}>
                Training Mode: {isTrainingMode ? 'ON' : 'OFF'}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={toggleTrainingMode}
                className={`glass-button-secondary px-4 py-2 ${
                  isTrainingMode ? 'bg-green-500/20' : 'bg-gray-500/20'
                }`}
              >
                {isTrainingMode ? 'Disable Training' : 'Enable Training'}
              </button>
              <button
                onClick={onClose}
                className="glass-button-secondary p-2 hover:bg-red-500/20"
              >
                ✕
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Training Examples Section */}
            <div className="space-y-4">
              {/* Add New Training Example */}
              <div className="glass-card p-4">
                <div className="flex items-center gap-2 mb-4">
                  <Plus className="w-5 h-5 text-blue-400" />
                  <h3 className="text-lg font-semibold text-white/90">Add Training Example</h3>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <label className="text-sm text-white/70 mb-1 block">Scenario/Input</label>
                    <textarea
                      value={newExample.scenario}
                      onChange={(e) => setNewExample(prev => ({ ...prev, scenario: e.target.value }))}
                      placeholder="Describe the situation or user input..."
                      className="glass-input w-full h-20 text-white placeholder:text-white/50 resize-none"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm text-white/70 mb-1 block">Expected Response</label>
                    <textarea
                      value={newExample.expected_response}
                      onChange={(e) => setNewExample(prev => ({ ...prev, expected_response: e.target.value }))}
                      placeholder="How the AI should respond in this situation..."
                      className="glass-input w-full h-20 text-white placeholder:text-white/50 resize-none"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="text-sm text-white/70 mb-1 block">Category</label>
                      <input
                        type="text"
                        value={newExample.category}
                        onChange={(e) => setNewExample(prev => ({ ...prev, category: e.target.value }))}
                        placeholder="e.g., speech_pattern, behavior"
                        className="glass-input w-full text-white placeholder:text-white/50"
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm text-white/70 mb-1 block">Priority (1-10)</label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={newExample.priority}
                        onChange={(e) => setNewExample(prev => ({ ...prev, priority: parseInt(e.target.value) || 5 }))}
                        className="glass-input w-full text-white"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm text-white/70 mb-1 block">Explanation (Optional)</label>
                    <textarea
                      value={newExample.explanation}
                      onChange={(e) => setNewExample(prev => ({ ...prev, explanation: e.target.value }))}
                      placeholder="Why this response is correct..."
                      className="glass-input w-full h-16 text-white placeholder:text-white/50 resize-none"
                    />
                  </div>
                  
                  <button
                    onClick={handleAddTrainingExample}
                    disabled={!newExample.scenario.trim() || !newExample.expected_response.trim()}
                    className="glass-button-primary w-full py-2 disabled:opacity-50 flex items-center justify-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Add Training Example
                  </button>
                </div>
              </div>

              {/* Feedback Section */}
              <div className="glass-card p-4">
                <div className="flex items-center gap-2 mb-4">
                  <Target className="w-5 h-5 text-orange-400" />
                  <h3 className="text-lg font-semibold text-white/90">Provide Feedback</h3>
                </div>
                
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <button
                      onClick={() => setFeedbackType('positive')}
                      className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                        feedbackType === 'positive' 
                          ? 'bg-green-500/30 text-green-300 border border-green-500/50' 
                          : 'bg-white/5 text-white/60 border border-white/10'
                      }`}
                    >
                      <CheckCircle className="w-4 h-4 inline mr-1" />
                      Positive
                    </button>
                    <button
                      onClick={() => setFeedbackType('negative')}
                      className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                        feedbackType === 'negative' 
                          ? 'bg-red-500/30 text-red-300 border border-red-500/50' 
                          : 'bg-white/5 text-white/60 border border-white/10'
                      }`}
                    >
                      <XCircle className="w-4 h-4 inline mr-1" />
                      Negative
                    </button>
                    <button
                      onClick={() => setFeedbackType('correction')}
                      className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                        feedbackType === 'correction' 
                          ? 'bg-orange-500/30 text-orange-300 border border-orange-500/50' 
                          : 'bg-white/5 text-white/60 border border-white/10'
                      }`}
                    >
                      <AlertCircle className="w-4 h-4 inline mr-1" />
                      Correction
                    </button>
                  </div>
                  
                  <textarea
                    value={feedbackText}
                    onChange={(e) => setFeedbackText(e.target.value)}
                    placeholder="Provide specific feedback about the AI's last response..."
                    className="glass-input w-full h-20 text-white placeholder:text-white/50 resize-none"
                  />
                  
                  <button
                    onClick={handleProvideFeedback}
                    disabled={!feedbackText.trim()}
                    className="glass-button-primary w-full py-2 disabled:opacity-50"
                  >
                    Apply Feedback
                  </button>
                </div>
              </div>
            </div>

            {/* Training Data Display */}
            <div className="space-y-4">
              {/* Behavioral Patterns */}
              <div className="glass-card p-4">
                <div className="flex items-center gap-2 mb-4">
                  <Brain className="w-5 h-5 text-green-400" />
                  <h3 className="text-lg font-semibold text-white/90">Learned Patterns</h3>
                  <span className="text-sm text-white/60">({behavioralPatterns.length})</span>
                </div>
                
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {behavioralPatterns.length > 0 ? (
                    behavioralPatterns.map((pattern) => (
                      <div key={pattern.id} className="bg-white/5 p-3 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-white/90 text-sm">{pattern.pattern_name}</h4>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-green-400">
                              {Math.round(pattern.confidence_score * 100)}%
                            </span>
                            <span className="text-xs text-white/50">
                              Used {pattern.usage_count}x
                            </span>
                          </div>
                        </div>
                        <p className="text-white/60 text-xs">
                          {pattern.pattern_description.length > 100 
                            ? `${pattern.pattern_description.substring(0, 100)}...` 
                            : pattern.pattern_description}
                        </p>
                      </div>
                    ))
                  ) : (
                    <p className="text-white/50 text-sm text-center py-4">
                      No patterns learned yet. Add training examples to help the AI learn!
                    </p>
                  )}
                </div>
              </div>

              {/* Training Examples */}
              <div className="glass-card p-4">
                <div className="flex items-center gap-2 mb-4">
                  <TrendingUp className="w-5 h-5 text-blue-400" />
                  <h3 className="text-lg font-semibold text-white/90">Training Examples</h3>
                  <span className="text-sm text-white/60">({trainingExamples.length})</span>
                </div>
                
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {trainingExamples.length > 0 ? (
                    trainingExamples.map((example) => (
                      <div key={example.id} className="bg-white/5 p-3 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <span className="text-xs text-blue-400 font-medium">
                            {example.category || 'General'}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-white/50">
                              Priority: {example.priority}
                            </span>
                            <span className="text-xs text-green-400">
                              {Math.round(example.effectiveness_score * 100)}% effective
                            </span>
                          </div>
                        </div>
                        <p className="text-white/80 text-sm mb-1">
                          <strong>Scenario:</strong> {example.scenario.length > 80 
                            ? `${example.scenario.substring(0, 80)}...` 
                            : example.scenario}
                        </p>
                        <p className="text-white/60 text-xs">
                          <strong>Response:</strong> {example.expected_response.length > 80 
                            ? `${example.expected_response.substring(0, 80)}...` 
                            : example.expected_response}
                        </p>
                      </div>
                    ))
                  ) : (
                    <p className="text-white/50 text-sm text-center py-4">
                      No training examples yet. Add some to start training!
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Help Text */}
          <div className="mt-6 glass-card p-4">
            <h3 className="text-lg font-semibold text-white/90 mb-2">Training Guide</h3>
            <div className="text-white/60 text-sm space-y-1">
              <p>• <strong>Training Examples:</strong> Provide specific scenarios and how the AI should respond</p>
              <p>• <strong>Feedback:</strong> Correct the AI when it responds incorrectly to improve learning</p>
              <p>• <strong>Training Mode:</strong> When enabled, the AI is more receptive to learning and changes</p>
              <p>• <strong>Patterns:</strong> The AI automatically learns behavioral patterns from your examples</p>
              <p>• <strong>Categories:</strong> Group similar training examples (e.g., "speech_pattern", "humor", "formal")</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TrainingInterface
