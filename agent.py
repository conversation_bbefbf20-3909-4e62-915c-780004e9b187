from flask import Blueprint, request, jsonify
from src.services.agent_service import AgentService
import logging

agent_bp = Blueprint('agent', __name__)
agent_service = AgentService()

@agent_bp.route('/execute-command', methods=['POST'])
def execute_command():
    """Execute a natural language command"""
    try:
        data = request.get_json()
        command = data.get('command', '')
        context = data.get('context', {})
        conversation_id = data.get('conversation_id', None)
        
        if not command:
            return jsonify({'success': False, 'error': 'Command is required'}), 400
        
        result = agent_service.execute_command(command, context, conversation_id)
        
        return jsonify({
            'success': True,
            'result': result,
            'command': command
        })
        
    except Exception as e:
        logging.error(f"Error executing command: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@agent_bp.route('/available-commands', methods=['GET'])
def get_available_commands():
    """Get list of available commands the agent can execute"""
    try:
        commands = agent_service.get_available_commands()
        
        return jsonify({
            'success': True,
            'commands': commands
        })
        
    except Exception as e:
        logging.error(f"Error getting available commands: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@agent_bp.route('/command-history', methods=['GET'])
def get_command_history():
    """Get history of executed commands"""
    try:
        user_id = request.args.get('user_id', 1, type=int)
        limit = request.args.get('limit', 50, type=int)
        
        history = agent_service.get_command_history(user_id, limit)
        
        return jsonify({
            'success': True,
            'history': history
        })
        
    except Exception as e:
        logging.error(f"Error getting command history: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@agent_bp.route('/parse-command', methods=['POST'])
def parse_command():
    """Parse a natural language command to understand intent and parameters"""
    try:
        data = request.get_json()
        command = data.get('command', '')
        
        if not command:
            return jsonify({'success': False, 'error': 'Command is required'}), 400
        
        parsed_result = agent_service.parse_command(command)
        
        return jsonify({
            'success': True,
            'parsed_command': parsed_result,
            'original_command': command
        })
        
    except Exception as e:
        logging.error(f"Error parsing command: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@agent_bp.route('/train-command', methods=['POST'])
def train_command():
    """Train the agent with a new command pattern"""
    try:
        data = request.get_json()
        command_pattern = data.get('pattern', '')
        action = data.get('action', '')
        parameters = data.get('parameters', {})
        
        if not command_pattern or not action:
            return jsonify({'success': False, 'error': 'Pattern and action are required'}), 400
        
        result = agent_service.train_command(command_pattern, action, parameters)
        
        return jsonify({
            'success': True,
            'result': result,
            'pattern': command_pattern
        })
        
    except Exception as e:
        logging.error(f"Error training command: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@agent_bp.route('/youtube/channels', methods=['GET'])
def get_youtube_channels():
    """Get YouTube channels (placeholder for YouTube API integration)"""
    try:
        channels = agent_service.get_youtube_channels()
        
        return jsonify({
            'success': True,
            'channels': channels
        })
        
    except Exception as e:
        logging.error(f"Error getting YouTube channels: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@agent_bp.route('/youtube/videos', methods=['GET'])
def get_youtube_videos():
    """Get YouTube videos for a channel"""
    try:
        channel_id = request.args.get('channel_id', '')
        limit = request.args.get('limit', 10, type=int)
        
        videos = agent_service.get_youtube_videos(channel_id, limit)
        
        return jsonify({
            'success': True,
            'videos': videos
        })
        
    except Exception as e:
        logging.error(f"Error getting YouTube videos: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@agent_bp.route('/youtube/upload', methods=['POST'])
def upload_youtube_video():
    """Upload a video to YouTube (placeholder)"""
    try:
        data = request.get_json()
        video_data = data.get('video_data', {})
        
        result = agent_service.upload_youtube_video(video_data)
        
        return jsonify({
            'success': True,
            'result': result
        })
        
    except Exception as e:
        logging.error(f"Error uploading YouTube video: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@agent_bp.route('/youtube/analytics', methods=['GET'])
def get_youtube_analytics():
    """Get YouTube analytics data"""
    try:
        channel_id = request.args.get('channel_id', '')
        date_range = request.args.get('date_range', '30d')
        
        analytics = agent_service.get_youtube_analytics(channel_id, date_range)
        
        return jsonify({
            'success': True,
            'analytics': analytics
        })
        
    except Exception as e:
        logging.error(f"Error getting YouTube analytics: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

