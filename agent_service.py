import os
import re
import json
from datetime import datetime
from src.services.ai_service import AIService
import logging

class AgentService:
    def __init__(self):
        self.ai_service = AIService()
        self.youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        
        # Command patterns and their corresponding actions
        self.command_patterns = {
            r'create.*script.*about (.+)': 'create_script',
            r'generate.*story.*about (.+)': 'create_story',
            r'write.*description.*for (.+)': 'create_description',
            r'upload.*video.*(.+)': 'upload_video',
            r'get.*analytics.*for (.+)': 'get_analytics',
            r'list.*videos': 'list_videos',
            r'create.*thumbnail.*for (.+)': 'create_thumbnail',
            r'schedule.*video.*(.+)': 'schedule_video',
            r'help.*with (.+)': 'general_help'
        }
        
        # Available commands for the agent
        self.available_commands = [
            {
                'name': 'create_script',
                'description': 'Create a YouTube video script about a specific topic',
                'example': 'Create a script about cooking pasta',
                'parameters': ['topic', 'duration', 'style']
            },
            {
                'name': 'create_story',
                'description': 'Generate a story for video content',
                'example': 'Generate a story about adventure travel',
                'parameters': ['topic', 'genre', 'length']
            },
            {
                'name': 'create_description',
                'description': 'Write a video description',
                'example': 'Write a description for my cooking video',
                'parameters': ['video_topic', 'keywords']
            },
            {
                'name': 'upload_video',
                'description': 'Upload a video to YouTube',
                'example': 'Upload my latest cooking video',
                'parameters': ['title', 'description', 'tags', 'file_path']
            },
            {
                'name': 'get_analytics',
                'description': 'Get analytics data for videos or channel',
                'example': 'Get analytics for my channel',
                'parameters': ['video_id', 'date_range']
            },
            {
                'name': 'list_videos',
                'description': 'List videos on the channel',
                'example': 'List my recent videos',
                'parameters': ['limit', 'order']
            }
        ]
    
    def execute_command(self, command, context=None, conversation_id=None):
        """Execute a natural language command"""
        try:
            # Parse the command to understand intent
            parsed_command = self.parse_command(command)
            
            intent = parsed_command.get('intent', 'unknown')
            parameters = parsed_command.get('parameters', {})
            
            # Execute based on intent
            if intent == 'create_script':
                return self._create_script(parameters, command)
            elif intent == 'create_story':
                return self._create_story(parameters, command)
            elif intent == 'create_description':
                return self._create_description(parameters, command)
            elif intent == 'upload_video':
                return self._upload_video(parameters)
            elif intent == 'get_analytics':
                return self._get_analytics(parameters)
            elif intent == 'list_videos':
                return self._list_videos(parameters)
            elif intent == 'general_help':
                return self._provide_help(parameters, command)
            else:
                return self._handle_unknown_command(command)
                
        except Exception as e:
            logging.error(f"Error executing command: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'I encountered an error while trying to execute that command.'
            }
    
    def parse_command(self, command):
        """Parse natural language command to extract intent and parameters"""
        try:
            # Use AI service to analyze the command
            ai_analysis = self.ai_service.analyze_command_intent(command)
            
            # Also try pattern matching as fallback
            pattern_match = self._pattern_match_command(command)
            
            # Combine results, preferring AI analysis if confident
            if ai_analysis.get('confidence', 0) > 0.7:
                return ai_analysis
            elif pattern_match:
                return pattern_match
            else:
                return ai_analysis  # Return AI analysis even if low confidence
                
        except Exception as e:
            logging.error(f"Error parsing command: {str(e)}")
            return {
                'intent': 'unknown',
                'parameters': {},
                'confidence': 0.0,
                'error': str(e)
            }
    
    def get_available_commands(self):
        """Get list of available commands"""
        return self.available_commands
    
    def get_command_history(self, user_id, limit=50):
        """Get command execution history (placeholder)"""
        # In a real implementation, this would query a database
        return {
            'history': [],
            'message': 'Command history feature coming soon'
        }
    
    def train_command(self, pattern, action, parameters):
        """Train the agent with new command patterns"""
        try:
            # Add new pattern to command patterns
            self.command_patterns[pattern] = action
            
            return {
                'success': True,
                'message': f'Successfully trained new command pattern: {pattern}',
                'pattern': pattern,
                'action': action
            }
            
        except Exception as e:
            logging.error(f"Error training command: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_youtube_channels(self):
        """Get YouTube channels (placeholder)"""
        # This would integrate with YouTube Data API
        return [
            {
                'id': 'UC_example_channel_id',
                'title': 'My YouTube Channel',
                'subscriber_count': 10000,
                'video_count': 50
            }
        ]
    
    def get_youtube_videos(self, channel_id, limit=10):
        """Get YouTube videos for a channel (placeholder)"""
        # This would integrate with YouTube Data API
        return [
            {
                'id': 'video_id_1',
                'title': 'Sample Video 1',
                'views': 1000,
                'likes': 50,
                'published_at': '2024-01-01T00:00:00Z'
            },
            {
                'id': 'video_id_2',
                'title': 'Sample Video 2',
                'views': 2000,
                'likes': 100,
                'published_at': '2024-01-02T00:00:00Z'
            }
        ]
    
    def upload_youtube_video(self, video_data):
        """Upload video to YouTube (placeholder)"""
        # This would integrate with YouTube Data API
        return {
            'success': True,
            'video_id': 'new_video_id',
            'message': 'Video upload functionality coming soon'
        }
    
    def get_youtube_analytics(self, channel_id, date_range):
        """Get YouTube analytics (placeholder)"""
        # This would integrate with YouTube Analytics API
        return {
            'views': 50000,
            'watch_time': 10000,
            'subscribers_gained': 100,
            'revenue': 250.00,
            'date_range': date_range
        }
    
    def _create_script(self, parameters, original_command):
        """Create a video script"""
        try:
            topic = parameters.get('topic') or parameters.get('query') or original_command
            duration = parameters.get('duration', '5-10 minutes')
            style = parameters.get('style', 'engaging and informative')
            
            script = self.ai_service.generate_content('script', topic, {
                'duration': duration,
                'style': style
            })
            
            return {
                'success': True,
                'content_type': 'script',
                'content': script,
                'topic': topic,
                'message': f'I\'ve created a script about {topic}. Here it is:'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble creating the script. Please try again.'
            }
    
    def _create_story(self, parameters, original_command):
        """Create a story"""
        try:
            topic = parameters.get('topic') or parameters.get('query') or original_command
            genre = parameters.get('genre', 'general')
            length = parameters.get('length', 'medium')
            
            story = self.ai_service.generate_content('story', topic, {
                'genre': genre,
                'length': length
            })
            
            return {
                'success': True,
                'content_type': 'story',
                'content': story,
                'topic': topic,
                'message': f'I\'ve created a story about {topic}. Here it is:'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble creating the story. Please try again.'
            }
    
    def _create_description(self, parameters, original_command):
        """Create a video description"""
        try:
            topic = parameters.get('topic') or parameters.get('query') or original_command
            
            description = self.ai_service.generate_content('description', topic)
            
            return {
                'success': True,
                'content_type': 'description',
                'content': description,
                'topic': topic,
                'message': f'I\'ve created a video description for {topic}. Here it is:'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'I had trouble creating the description. Please try again.'
            }
    
    def _upload_video(self, parameters):
        """Upload a video (placeholder)"""
        return {
            'success': False,
            'message': 'Video upload functionality is coming soon. I can help you prepare the video details in the meantime.'
        }
    
    def _get_analytics(self, parameters):
        """Get analytics data (placeholder)"""
        return {
            'success': True,
            'message': 'Analytics functionality is coming soon. I can help you understand what metrics to track.',
            'data': self.get_youtube_analytics('', '30d')
        }
    
    def _list_videos(self, parameters):
        """List videos (placeholder)"""
        videos = self.get_youtube_videos('', parameters.get('limit', 10))
        return {
            'success': True,
            'message': 'Here are your recent videos:',
            'videos': videos
        }
    
    def _provide_help(self, parameters, command):
        """Provide help and guidance"""
        help_topic = parameters.get('topic') or parameters.get('query') or 'general'
        
        help_response = self.ai_service.generate_response(
            f"Provide helpful guidance about {help_topic} for YouTube content creation"
        )
        
        return {
            'success': True,
            'message': help_response,
            'available_commands': self.available_commands
        }
    
    def _handle_unknown_command(self, command):
        """Handle unknown commands"""
        suggestion = self.ai_service.generate_response(
            f"The user said: '{command}'. This seems to be related to YouTube content creation. "
            f"Provide a helpful response and suggest what they might want to do."
        )
        
        return {
            'success': True,
            'message': suggestion,
            'available_commands': self.available_commands
        }
    
    def _pattern_match_command(self, command):
        """Match command against predefined patterns"""
        command_lower = command.lower()
        
        for pattern, action in self.command_patterns.items():
            match = re.search(pattern, command_lower)
            if match:
                parameters = {}
                if match.groups():
                    parameters['topic'] = match.group(1).strip()
                
                return {
                    'intent': action,
                    'parameters': parameters,
                    'confidence': 0.8,
                    'method': 'pattern_matching'
                }
        
        return None

