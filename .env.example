# Flask Configuration
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# SocketIO Configuration
SOCKETIO_ASYNC_MODE=threading

# AI Service Configuration - Get from https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-api-key-here

# Voice Service Configuration - Get from https://elevenlabs.io/
ELEVENLABS_API_KEY=your-elevenlabs-api-key-here
DEFAULT_VOICE_ID=21m00Tcm4TlvDq8ikWAM

# YouTube API (for agent service) - Get from Google Cloud Console
YOUTUBE_API_KEY=your-youtube-api-key-here

# Database Configuration
DATABASE_URL=sqlite:///database/app.db
