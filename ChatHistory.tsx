import React, { useEffect, useRef } from 'react'
import { Card, CardContent } from '@/components/ui/card.jsx'
import { Avatar, AvatarFallback } from '@/components/ui/avatar.jsx'
import { <PERSON>r, <PERSON><PERSON>, Loader2, <PERSON>rk<PERSON> } from 'lucide-react'

interface Message {
  id: number
  content: string
  message_type: 'user' | 'assistant' | 'system'
  created_at: string
  audio_url?: string
  metadata?: any
}

interface ChatHistoryProps {
  messages: Message[]
  isLoading: boolean
}

const ChatHistory: React.FC<ChatHistoryProps> = ({ messages, isLoading }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const isCommandMessage = (message: Message) => {
    return message.metadata?.type === 'command' || message.metadata?.type === 'command_result'
  }

  const renderMessage = (message: Message) => {
    const isUser = message.message_type === 'user'
    const isCommand = isCommandMessage(message)

    return (
      <div
        key={message.id}
        className={`flex gap-3 mb-6 ${isUser ? 'flex-row-reverse' : 'flex-row'} animate-in slide-in-from-bottom-2 duration-300`}
      >
        {/* Avatar */}
        <div className={`flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`}>
          <div className={`w-10 h-10 rounded-2xl flex items-center justify-center ${
            isUser 
              ? 'bg-blue-500/20 border border-blue-400/30' 
              : 'bg-purple-500/20 border border-purple-400/30'
          } backdrop-blur-md shadow-lg`}>
            {isUser ? (
              <User className="w-5 h-5 text-blue-300" />
            ) : (
              <Bot className="w-5 h-5 text-purple-300" />
            )}
          </div>
        </div>

        {/* Message Content */}
        <div className={`flex-1 max-w-[75%] ${isUser ? 'text-right' : 'text-left'}`}>
          <div className={`${
            isUser 
              ? 'chat-bubble-user' 
              : isCommand 
                ? 'backdrop-blur-md bg-emerald-500/15 border border-emerald-400/25 rounded-3xl p-4 shadow-lg text-emerald-100 mr-auto max-w-[80%]' 
                : 'chat-bubble-assistant'
          } transition-all duration-200 hover:shadow-xl`}>
            
            {/* Command indicator */}
            {isCommand && (
              <div className="flex items-center gap-2 text-xs text-emerald-300 font-medium mb-2">
                <Sparkles className="w-3 h-3" />
                {message.metadata?.type === 'command' ? 'Command' : 'Result'}
              </div>
            )}
            
            {/* Message text */}
            <div className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.content}
            </div>

            {/* Command result details */}
            {message.metadata?.command_result && (
              <div className="mt-3 pt-3 border-t border-emerald-400/20">
                <div className="text-xs text-emerald-200">
                  {message.metadata.command_result.success ? (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                      <span>Command executed successfully</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                      <span>Command failed</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Timestamp */}
            <div className={`text-xs mt-3 opacity-60 ${
              isUser ? 'text-blue-200' : 'text-gray-300'
            }`}>
              {formatTime(message.created_at)}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 overflow-y-auto p-6 space-y-2 chat-scroll">
      {messages.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <div className="glass-panel rounded-3xl p-8 max-w-sm">
            <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-purple-500/20 to-blue-500/20 border border-white/20 flex items-center justify-center backdrop-blur-md">
              <Bot className="w-8 h-8 text-white/70" />
            </div>
            <h3 className="text-lg font-semibold text-white/90 mb-2">
              Start a conversation
            </h3>
            <p className="text-white/60 text-sm">
              Ask me anything or use voice commands to create content for your YouTube channel!
            </p>
          </div>
        </div>
      ) : (
        <>
          {messages.map(renderMessage)}
          
          {/* Loading indicator */}
          {isLoading && (
            <div className="flex gap-3 mb-6 animate-in slide-in-from-bottom-2 duration-300">
              <div className="flex-shrink-0 mr-3">
                <div className="w-10 h-10 rounded-2xl flex items-center justify-center bg-purple-500/20 border border-purple-400/30 backdrop-blur-md shadow-lg">
                  <Bot className="w-5 h-5 text-purple-300" />
                </div>
              </div>
              <div className="flex-1">
                <div className="chat-bubble-assistant">
                  <div className="flex items-center gap-3 text-sm text-gray-300">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>AI is thinking...</span>
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
      
      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  )
}

export default ChatHistory

