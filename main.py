import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory
from flask_cors import CORS
from flask_socketio import Socket<PERSON>
from src.models.user import db, User
from src.models.conversation import Conversation
from src.models.message import Message
from src.models.personality_memory import (
    PersonalityProfile, PersonalityMemory, TrainingExample,
    BehavioralPattern, PersonalityEvolution, MemoryTag
)
from src.routes.user import user_bp
from src.routes.chat import chat_bp
from src.routes.voice import voice_bp
from src.routes.agent import agent_bp
from src.websocket.chat_handler import register_chat_handlers

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))

# Configuration
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'asdf#FGSgvasgf$5$WGT')
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
CORS(app, origins=os.getenv('CORS_ORIGINS', '*').split(','))
socketio = SocketIO(app, cors_allowed_origins="*", async_mode=os.getenv('SOCKETIO_ASYNC_MODE', 'threading'))

# Register blueprints
app.register_blueprint(user_bp, url_prefix='/api')
app.register_blueprint(chat_bp, url_prefix='/api/chat')
app.register_blueprint(voice_bp, url_prefix='/api/voice')
app.register_blueprint(agent_bp, url_prefix='/api/agent')

# Initialize database
db.init_app(app)
with app.app_context():
    db.create_all()

# Register WebSocket handlers
register_chat_handlers(socketio)

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
        return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404

@app.route('/health')
def health_check():
    return {'status': 'healthy', 'service': 'conversational-ai-backend'}

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)

