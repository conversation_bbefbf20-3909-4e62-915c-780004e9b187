#!/usr/bin/env python3
"""
Test script for the advanced personality training system
"""

import os
import sys
from datetime import datetime

# Add the src directory to the path
sys.path.append('src')

from models.user import db
from models.personality_memory import PersonalityProfile, TrainingExample
from services.memory_service import MemoryService
from services.learning_engine import LearningEngine
from services.ai_service import AIService

def test_personality_system():
    """Test the personality training system"""
    print("🧠 Testing Advanced Personality Training System...")
    
    # Initialize services
    memory_service = MemoryService()
    learning_engine = LearningEngine()
    ai_service = AIService()
    
    print("✅ Services initialized")
    
    # Test 1: Create a personality profile
    print("\n📝 Test 1: Creating personality profile...")
    
    # Create test personality
    personality = PersonalityProfile(
        name="test_wise_mentor",
        base_prompt="You are a wise mentor who speaks calmly and gives thoughtful advice. You use metaphors from nature and always encourage learning.",
        learning_enabled=True,
        training_mode=True
    )
    
    try:
        db.session.add(personality)
        db.session.commit()
        print(f"✅ Created personality: {personality.name}")
        
        # Set as active personality
        memory_service.set_active_personality(personality.id)
        ai_service.set_personality(personality.base_prompt)
        print(f"✅ Set active personality: {personality.id}")
        
    except Exception as e:
        print(f"❌ Error creating personality: {e}")
        return False
    
    # Test 2: Add training examples
    print("\n📚 Test 2: Adding training examples...")
    
    training_examples = [
        {
            "scenario": "User asks for advice about learning programming",
            "expected_response": "Learning to code is like tending a garden, my friend. Start with small seeds - basic concepts - and water them daily with practice. Don't rush the growth; even the mightiest oak started as a tiny acorn.",
            "category": "advice_giving",
            "explanation": "Uses nature metaphor and encouraging tone"
        },
        {
            "scenario": "User is frustrated with making mistakes",
            "expected_response": "Ah, mistakes are like fallen leaves in autumn - they may seem like failures, but they nourish the soil for new growth. Each error teaches us something valuable. Embrace them as stepping stones on your path.",
            "category": "emotional_support", 
            "explanation": "Reframes mistakes positively using nature metaphor"
        },
        {
            "scenario": "User asks about persistence",
            "expected_response": "Consider the river, dear student. It doesn't fight the mountain, yet over time it carves the deepest canyons. Persistence is not about force, but about consistent, gentle effort toward your goal.",
            "category": "wisdom_sharing",
            "explanation": "Uses water/river metaphor to teach about persistence"
        }
    ]
    
    for example_data in training_examples:
        try:
            example = memory_service.add_training_example(
                scenario=example_data["scenario"],
                expected_response=example_data["expected_response"],
                explanation=example_data["explanation"],
                category=example_data["category"],
                priority=8
            )
            if example:
                print(f"✅ Added training example: {example_data['category']}")
            else:
                print(f"❌ Failed to add training example: {example_data['category']}")
        except Exception as e:
            print(f"❌ Error adding training example: {e}")
    
    # Test 3: Test memory retrieval
    print("\n🔍 Test 3: Testing memory retrieval...")
    
    test_queries = [
        "I'm struggling to learn new things",
        "I keep making errors in my code",
        "How do I stay motivated?"
    ]
    
    for query in test_queries:
        try:
            memories = memory_service.get_relevant_memories(query, limit=3)
            print(f"✅ Query: '{query}' -> Found {len(memories)} relevant memories")
        except Exception as e:
            print(f"❌ Error retrieving memories for '{query}': {e}")
    
    # Test 4: Test pattern detection
    print("\n🧩 Test 4: Testing pattern detection...")
    
    try:
        patterns = learning_engine.detect_behavioral_patterns(personality.id)
        print(f"✅ Detected {len(patterns)} behavioral patterns")
        
        for pattern in patterns:
            print(f"   - {pattern.pattern_name}: {pattern.confidence_score:.2f} confidence")
            
    except Exception as e:
        print(f"❌ Error detecting patterns: {e}")
    
    # Test 5: Test AI response with personality
    print("\n🤖 Test 5: Testing AI response generation...")
    
    test_message = "I'm feeling discouraged about my progress in learning"
    
    try:
        response = ai_service.generate_response(
            test_message, 
            conversation_id=None, 
            memory_service=memory_service
        )
        print(f"✅ Generated response:")
        print(f"   User: {test_message}")
        print(f"   AI: {response}")
        
        # Check if response contains nature metaphors (personality trait)
        nature_words = ['tree', 'river', 'mountain', 'garden', 'seed', 'oak', 'leaf', 'autumn', 'spring', 'forest', 'stone', 'path']
        has_nature_metaphor = any(word in response.lower() for word in nature_words)
        
        if has_nature_metaphor:
            print("✅ Response contains nature metaphor (personality trait detected)")
        else:
            print("⚠️  Response doesn't contain obvious nature metaphor")
            
    except Exception as e:
        print(f"❌ Error generating AI response: {e}")
    
    # Test 6: Test feedback system
    print("\n📝 Test 6: Testing feedback system...")
    
    try:
        # Get the last memory (from the AI response test)
        recent_memories = memory_service.get_relevant_memories("recent interaction", limit=1)
        
        if recent_memories:
            memory = recent_memories[0]
            success = memory_service.provide_feedback(
                memory_id=memory.id,
                feedback_type='positive',
                feedback_text='Great use of nature metaphor and encouraging tone!',
                score=9.0
            )
            
            if success:
                print("✅ Applied positive feedback successfully")
            else:
                print("❌ Failed to apply feedback")
        else:
            print("⚠️  No recent memories found for feedback test")
            
    except Exception as e:
        print(f"❌ Error testing feedback: {e}")
    
    print("\n🎉 Personality training system test completed!")
    print("\nNext steps:")
    print("1. Add your OpenAI API key to .env file")
    print("2. Run the application: python3 main.py")
    print("3. Open the personality manager (🧠 button)")
    print("4. Open the training interface (📚 button)")
    print("5. Start training your AI personality!")
    
    return True

if __name__ == "__main__":
    # Set up Flask app context for database operations
    from main import app
    
    with app.app_context():
        # Create tables if they don't exist
        db.create_all()
        
        # Run the test
        test_personality_system()
